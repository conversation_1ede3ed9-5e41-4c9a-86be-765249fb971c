'use client';

import { useState, useCallback } from 'react';

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  isUploading: boolean;
}

export default function FileUpload({ onFileSelect, isUploading }: FileUploadProps) {
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.name.endsWith('.csv')) {
        onFileSelect(file);
      } else {
        alert('Please select a CSV file');
      }
    }
  }, [onFileSelect]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.name.endsWith('.csv')) {
        onFileSelect(file);
      } else {
        alert('Please select a CSV file');
      }
    }
  }, [onFileSelect]);

  return (
    <div className="w-full">
      <div
        className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
          dragActive
            ? 'border-blue-500 bg-blue-500/10'
            : 'border-slate-600 hover:border-slate-500'
        } ${isUploading ? 'opacity-50 pointer-events-none' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="text-slate-400 mb-4">
          <svg className="mx-auto h-12 w-12" stroke="currentColor" fill="none" viewBox="0 0 48 48">
            <path 
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round" 
            />
          </svg>
        </div>
        
        <h3 className="text-lg font-medium text-white mb-2">
          {isUploading ? 'Uploading...' : 'Upload CSV Tick Data Files'}
        </h3>
        
        <p className="text-slate-400 mb-4">
          {isUploading 
            ? 'Please wait while your file is being uploaded...'
            : 'Drag and drop your CSV files here, or click to browse'
          }
        </p>
        
        <p className="text-sm text-slate-500 mb-4">
          Supported formats: EURUSD_Ticks_*.csv, USDJPY_Ticks_*.csv, GBPUSD_Ticks_*.csv, USDCHF_Ticks_*.csv
        </p>
        
        <div className="space-y-2">
          <label className="cursor-pointer">
            <input
              type="file"
              accept=".csv"
              onChange={handleFileInput}
              className="hidden"
              disabled={isUploading}
            />
            <span className={`inline-block px-6 py-2 rounded-lg transition-colors ${
              isUploading
                ? 'bg-slate-600 text-slate-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white cursor-pointer'
            }`}>
              {isUploading ? 'Uploading...' : 'Select Files'}
            </span>
          </label>
        </div>

        {isUploading && (
          <div className="mt-4">
            <div className="w-full bg-slate-700 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
          </div>
        )}
      </div>

      {/* File Format Requirements */}
      <div className="mt-6 bg-slate-700/30 rounded-lg p-4">
        <h4 className="text-sm font-medium text-white mb-2">CSV File Format Requirements:</h4>
        <div className="text-sm text-slate-300 space-y-1">
          <p><strong>Header:</strong> Time (UTC), Ask, Bid, AskVol, BidVol</p>
          <p><strong>Timestamp Format:</strong> 2025-01-15 09:30:00.123</p>
          <p><strong>Symbol Detection:</strong> Extracted from filename (e.g., EURUSD_Ticks_20250115.csv)</p>
          <p><strong>Duplicate Prevention:</strong> Records with same symbol and timestamp are automatically skipped</p>
        </div>
      </div>
    </div>
  );
}
