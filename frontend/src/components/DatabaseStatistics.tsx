'use client';

import { useState, useEffect } from 'react';
import { ApiService, DatabaseStats, formatNumber, formatDate } from '@/lib/api';

export default function DatabaseStatistics() {
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const fetchStats = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await ApiService.getDatabaseStats();
      setStats(data);
      setLastRefresh(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const updateStats = async () => {
    setIsUpdating(true);
    setError(null);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}/api/stats/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ triggered_by: 'manual' }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update stats: ${response.status}`);
      }

      // After successful update, fetch the latest stats
      await fetchStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update statistics');
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  if (isLoading && !stats) {
    return (
      <div className="bg-slate-800/50 rounded-lg p-12 text-center">
        <div className="text-blue-500 mb-4">
          <svg className="mx-auto h-8 w-8 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <p className="text-slate-400">Loading database statistics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-600/30 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-start space-x-3">
            <div className="text-red-500 mt-0.5">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h5 className="text-red-400 font-medium mb-1">Error Loading Statistics</h5>
              <p className="text-red-200 text-sm">{error}</p>
            </div>
          </div>
          <button
            onClick={fetchStats}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className="space-y-6">
      {/* Header with Refresh and Update Stats */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-white mb-2">Database Statistics</h2>
          <p className="text-slate-400 text-sm">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={updateStats}
            disabled={isUpdating || isLoading}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              isUpdating || isLoading
                ? 'bg-slate-600 text-slate-400 cursor-not-allowed'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            <svg className={`w-4 h-4 ${isUpdating ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>{isUpdating ? 'Updating...' : 'Update Stats'}</span>
          </button>

          <button
            onClick={fetchStats}
            disabled={isLoading || isUpdating}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              isLoading || isUpdating
                ? 'bg-slate-600 text-slate-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            <svg className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>{isLoading ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg p-6 text-white">
          <h4 className="text-sm font-medium opacity-90 mb-1">Total Ticks</h4>
          <p className="text-3xl font-bold">{formatNumber(stats.total_ticks)}</p>
          <p className="text-sm opacity-75 mt-1">Raw tick records</p>
        </div>
        
        <div className="bg-gradient-to-br from-green-600 to-green-700 rounded-lg p-6 text-white">
          <h4 className="text-sm font-medium opacity-90 mb-1">Instruments</h4>
          <p className="text-3xl font-bold">{stats.total_instruments}</p>
          <p className="text-sm opacity-75 mt-1">Currency pairs</p>
        </div>
        
        <div className="bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg p-6 text-white">
          <h4 className="text-sm font-medium opacity-90 mb-1">Database Size</h4>
          <p className="text-3xl font-bold">{stats.database_size}</p>
          <p className="text-sm opacity-75 mt-1">Total storage</p>
        </div>
        
        <div className="bg-gradient-to-br from-orange-600 to-orange-700 rounded-lg p-6 text-white">
          <h4 className="text-sm font-medium opacity-90 mb-1">Chunks</h4>
          <p className="text-3xl font-bold">{stats.performance.chunk_count}</p>
          <p className="text-sm opacity-75 mt-1">TimescaleDB chunks</p>
        </div>
      </div>

      {/* Date Range */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-slate-700/50 rounded-lg p-6">
          <h4 className="text-lg font-medium text-white mb-4">Data Range</h4>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-slate-400">Oldest Record</p>
              <p className="text-white font-mono">{formatDate(stats.oldest_record)}</p>
            </div>
            <div>
              <p className="text-sm text-slate-400">Newest Record</p>
              <p className="text-white font-mono">{formatDate(stats.newest_record)}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-slate-700/50 rounded-lg p-6">
          <h4 className="text-lg font-medium text-white mb-4">Performance</h4>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-slate-400">Compression Ratio</p>
              <p className="text-white font-semibold">{stats.performance.compression_ratio}:1</p>
            </div>
            <div>
              <p className="text-sm text-slate-400">Average Query Time</p>
              <p className="text-white font-semibold">{stats.performance.avg_query_time}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Instruments Table */}
      <div className="bg-slate-700/50 rounded-lg overflow-hidden">
        <div className="p-6 border-b border-slate-600">
          <h4 className="text-lg font-medium text-white">Instrument Statistics</h4>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-slate-800/50">
              <tr>
                <th className="text-left p-4 text-slate-300 font-medium">Symbol</th>
                <th className="text-right p-4 text-slate-300 font-medium">Tick Count</th>
                <th className="text-right p-4 text-slate-300 font-medium">Date Range</th>
                <th className="text-right p-4 text-slate-300 font-medium">Price Range</th>
                <th className="text-right p-4 text-slate-300 font-medium">Spread Statistics</th>
                <th className="text-right p-4 text-slate-300 font-medium">Last Updated</th>
              </tr>
            </thead>
            <tbody>
              {stats.instruments.map((instrument) => (
                <tr key={instrument.symbol_id} className="border-t border-slate-600/50">
                  <td className="p-4">
                    <div className="flex items-center space-x-2">
                      <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                        {instrument.symbol}
                      </span>
                      <span className="text-slate-400 text-sm">ID: {instrument.symbol_id}</span>
                    </div>
                  </td>
                  <td className="p-4 text-right text-white font-mono">
                    {formatNumber(instrument.tick_count)}
                  </td>
                  <td className="p-4 text-right text-slate-300 text-sm">
                    <div>{formatDate(instrument.start_date).split(',')[0]}</div>
                    <div className="text-slate-400">to {formatDate(instrument.end_date).split(',')[0]}</div>
                  </td>
                  <td className="p-4 text-right text-slate-300 text-sm font-mono">
                    <div>Ask: {instrument.min_ask?.toFixed(5)} - {instrument.max_ask?.toFixed(5)}</div>
                    <div className="text-slate-400">Bid: {instrument.min_bid?.toFixed(5)} - {instrument.max_bid?.toFixed(5)}</div>
                  </td>
                  <td className="p-4 text-right text-slate-300 text-sm font-mono">
                    <div className="text-white">Avg: {((instrument.avg_spread || 0) * 10000).toFixed(1)} pips</div>
                    <div>Min: {((instrument.min_spread || 0) * 10000).toFixed(1)} pips</div>
                    <div>Max: {((instrument.max_spread || 0) * 10000).toFixed(1)} pips</div>
                    <div className="text-blue-300">Median: {((instrument.median_spread || 0) * 10000).toFixed(1)} pips</div>
                  </td>
                  <td className="p-4 text-right text-slate-300 text-sm">
                    {formatDate(instrument.last_updated)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Continuous Aggregates */}
      <div className="bg-slate-700/50 rounded-lg overflow-hidden">
        <div className="p-6 border-b border-slate-600">
          <h4 className="text-lg font-medium text-white">Continuous Aggregates (OHLC Views)</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
          {stats.continuous_aggregates.map((agg) => (
            <div key={agg.name} className="bg-slate-800/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h5 className="text-white font-medium">{agg.timeframe}</h5>
                <span className="bg-green-600 text-white px-2 py-1 rounded-full text-xs">
                  {agg.refresh_lag}
                </span>
              </div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-slate-400">Buckets:</span>
                  <span className="text-white font-mono">{formatNumber(agg.bucket_count)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Compression:</span>
                  <span className="text-white font-mono">{agg.compression_ratio.toFixed(1)}:1</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
