'use client';

import { formatNumber, formatDuration, formatDate } from '@/lib/api';

interface ImportResult {
  success: boolean;
  message: string;
  records_imported: number;
  records_skipped: number;
  duration: string;
  start_time: string;
  end_time: string;
}

interface ImportResultProps {
  result: ImportResult;
  onStartNew: () => void;
}

export default function ImportResult({ result, onStartNew }: ImportResultProps) {
  const totalRecords = result.records_imported + result.records_skipped;
  const importRate = totalRecords > 0 ? (result.records_imported / totalRecords) * 100 : 0;

  return (
    <div className="bg-slate-800/50 rounded-lg p-6 backdrop-blur-sm">
      {/* Success Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-green-600 rounded-full mb-4">
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-white mb-2">Import Completed Successfully!</h3>
        <p className="text-slate-300">{result.message}</p>
      </div>

      {/* Import Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-green-900/20 border border-green-600/30 rounded-lg p-4">
          <h4 className="text-sm font-medium text-green-400 mb-1">Records Imported</h4>
          <p className="text-2xl font-bold text-white">{formatNumber(result.records_imported)}</p>
        </div>
        
        <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
          <h4 className="text-sm font-medium text-yellow-400 mb-1">Records Skipped</h4>
          <p className="text-2xl font-bold text-white">{formatNumber(result.records_skipped)}</p>
          <p className="text-xs text-yellow-300 mt-1">Duplicates</p>
        </div>
        
        <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-400 mb-1">Import Rate</h4>
          <p className="text-2xl font-bold text-white">{importRate.toFixed(1)}%</p>
          <p className="text-xs text-blue-300 mt-1">Success Rate</p>
        </div>
        
        <div className="bg-slate-700/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-400 mb-1">Duration</h4>
          <p className="text-2xl font-bold text-white">{formatDuration(result.duration)}</p>
        </div>
      </div>

      {/* Timing Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        <div className="bg-slate-700/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-400 mb-1">Start Time</h4>
          <p className="text-white font-mono">{formatDate(result.start_time)}</p>
        </div>
        
        <div className="bg-slate-700/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-400 mb-1">End Time</h4>
          <p className="text-white font-mono">{formatDate(result.end_time)}</p>
        </div>
      </div>

      {/* Performance Metrics */}
      {result.records_imported > 0 && (
        <div className="bg-slate-700/30 rounded-lg p-4 mb-8">
          <h4 className="text-lg font-medium text-white mb-4">Performance Metrics</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h5 className="text-sm font-medium text-slate-400 mb-1">Records per Second</h5>
              <p className="text-lg font-semibold text-white">
                {(() => {
                  const durationSeconds = parseFloat(result.duration.replace('s', ''));
                  const rps = result.records_imported / durationSeconds;
                  return formatNumber(Math.round(rps));
                })()}
              </p>
            </div>
            
            <div>
              <h5 className="text-sm font-medium text-slate-400 mb-1">Total Processed</h5>
              <p className="text-lg font-semibold text-white">
                {formatNumber(totalRecords)}
              </p>
            </div>
            
            <div>
              <h5 className="text-sm font-medium text-slate-400 mb-1">Efficiency</h5>
              <p className="text-lg font-semibold text-white">
                {importRate > 90 ? 'Excellent' : importRate > 70 ? 'Good' : 'Fair'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Next Steps */}
      <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <div className="text-blue-500 mt-0.5">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h5 className="text-blue-400 font-medium mb-1">What Happens Next?</h5>
            <ul className="text-blue-200 text-sm space-y-1">
              <li>• Continuous aggregates have been automatically refreshed</li>
              <li>• New OHLC data is available for all timeframes (10s, 1m, 5m, etc.)</li>
              <li>• Database statistics have been updated</li>
              <li>• Data is ready for backtesting and analysis</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <button
          onClick={onStartNew}
          className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg transition-colors font-medium"
        >
          Import Another File
        </button>
        
        <button
          onClick={() => window.location.href = '/data-management?tab=statistics'}
          className="bg-slate-600 hover:bg-slate-500 text-white px-8 py-3 rounded-lg transition-colors font-medium"
        >
          View Database Statistics
        </button>
      </div>

      {/* Import Summary */}
      <div className="mt-8 text-center">
        <p className="text-slate-400 text-sm">
          Import completed at {formatDate(result.end_time)} • 
          {formatNumber(result.records_imported)} records added to TimescaleDB
        </p>
      </div>
    </div>
  );
}
