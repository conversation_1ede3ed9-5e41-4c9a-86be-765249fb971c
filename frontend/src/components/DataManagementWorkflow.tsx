'use client';

import { useState } from 'react';
import FileUpload from './FileUpload';
import ImportPreview from './ImportPreview';
import ImportResult from './ImportResult';
import { ApiService, FilePreview, UploadResponse, ImportResult as ImportResultType } from '@/lib/api';

type WorkflowStep = 'upload' | 'preview' | 'importing' | 'result';

export default function DataManagementWorkflow() {
  const [currentStep, setCurrentStep] = useState<WorkflowStep>('upload');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // State for each step
  const [uploadResponse, setUploadResponse] = useState<UploadResponse | null>(null);
  const [previewData, setPreviewData] = useState<FilePreview | null>(null);
  const [importResult, setImportResult] = useState<ImportResultType | null>(null);

  const handleFileSelect = async (file: File) => {
    setIsLoading(true);
    setError(null);

    try {
      // Upload file
      const uploadResp = await ApiService.uploadFile(file);
      setUploadResponse(uploadResp);

      // Get preview using upload response (contains symbol info)
      const preview = await ApiService.previewFile(uploadResp);
      setPreviewData(preview);

      setCurrentStep('preview');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportConfirm = async () => {
    if (!uploadResponse || !previewData) return;

    setCurrentStep('importing');
    setIsLoading(true);
    setError(null);

    try {
      const result = await ApiService.executeImport(
        uploadResponse.temp_path,
        uploadResponse.symbol_id
      );
      setImportResult(result);
      setCurrentStep('result');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Import failed');
      setCurrentStep('preview'); // Go back to preview on error
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportCancel = () => {
    setCurrentStep('upload');
    setUploadResponse(null);
    setPreviewData(null);
    setImportResult(null);
    setError(null);
  };

  const handleStartNew = () => {
    setCurrentStep('upload');
    setUploadResponse(null);
    setPreviewData(null);
    setImportResult(null);
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <div className="bg-slate-800/30 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Import Progress</h3>
          <div className="text-sm text-slate-400">
            Step {currentStep === 'upload' ? '1' : currentStep === 'preview' ? '2' : currentStep === 'importing' ? '3' : '4'} of 4
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Step 1: Upload */}
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === 'upload' 
                ? 'bg-blue-600 text-white' 
                : ['preview', 'importing', 'result'].includes(currentStep)
                ? 'bg-green-600 text-white'
                : 'bg-slate-600 text-slate-300'
            }`}>
              {['preview', 'importing', 'result'].includes(currentStep) ? '✓' : '1'}
            </div>
            <span className="ml-2 text-sm text-slate-300">Upload</span>
          </div>
          
          <div className="flex-1 h-0.5 bg-slate-600">
            <div className={`h-full transition-all duration-300 ${
              ['preview', 'importing', 'result'].includes(currentStep) ? 'bg-green-600 w-full' : 'bg-slate-600 w-0'
            }`}></div>
          </div>
          
          {/* Step 2: Preview */}
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === 'preview' 
                ? 'bg-blue-600 text-white' 
                : ['importing', 'result'].includes(currentStep)
                ? 'bg-green-600 text-white'
                : 'bg-slate-600 text-slate-300'
            }`}>
              {['importing', 'result'].includes(currentStep) ? '✓' : '2'}
            </div>
            <span className="ml-2 text-sm text-slate-300">Preview</span>
          </div>
          
          <div className="flex-1 h-0.5 bg-slate-600">
            <div className={`h-full transition-all duration-300 ${
              ['importing', 'result'].includes(currentStep) ? 'bg-green-600 w-full' : 'bg-slate-600 w-0'
            }`}></div>
          </div>
          
          {/* Step 3: Import */}
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === 'importing' 
                ? 'bg-blue-600 text-white' 
                : currentStep === 'result'
                ? 'bg-green-600 text-white'
                : 'bg-slate-600 text-slate-300'
            }`}>
              {currentStep === 'result' ? '✓' : currentStep === 'importing' ? '⟳' : '3'}
            </div>
            <span className="ml-2 text-sm text-slate-300">Import</span>
          </div>
          
          <div className="flex-1 h-0.5 bg-slate-600">
            <div className={`h-full transition-all duration-300 ${
              currentStep === 'result' ? 'bg-green-600 w-full' : 'bg-slate-600 w-0'
            }`}></div>
          </div>
          
          {/* Step 4: Complete */}
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === 'result' 
                ? 'bg-green-600 text-white' 
                : 'bg-slate-600 text-slate-300'
            }`}>
              {currentStep === 'result' ? '✓' : '4'}
            </div>
            <span className="ml-2 text-sm text-slate-300">Complete</span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-600/30 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="text-red-500 mt-0.5">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h5 className="text-red-400 font-medium mb-1">Error</h5>
              <p className="text-red-200 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Step Content */}
      {currentStep === 'upload' && (
        <FileUpload 
          onFileSelect={handleFileSelect}
          isUploading={isLoading}
        />
      )}

      {currentStep === 'preview' && previewData && (
        <ImportPreview
          preview={previewData}
          onConfirm={handleImportConfirm}
          onCancel={handleImportCancel}
          isImporting={false}
        />
      )}

      {currentStep === 'importing' && (
        <div className="bg-slate-800/50 rounded-lg p-12 text-center">
          <div className="text-blue-500 mb-4">
            <svg className="mx-auto h-12 w-12 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Importing Data...</h3>
          <p className="text-slate-400">
            Please wait while your tick data is being imported into TimescaleDB.
            This may take a few moments depending on file size.
          </p>
        </div>
      )}

      {currentStep === 'result' && importResult && (
        <ImportResult
          result={importResult}
          onStartNew={handleStartNew}
        />
      )}
    </div>
  );
}
