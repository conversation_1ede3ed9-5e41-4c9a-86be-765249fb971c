'use client';

interface TickRow {
  utc: string;
  ask: number;
  bid: number;
  ask_vol?: number;
  bid_vol?: number;
}

interface FilePreview {
  filename: string;
  symbol: string;
  symbol_id: number;
  record_count: number;
  start_date: string;
  end_date: string;
  file_size: number;
  sample_data: TickRow[];
}

interface ImportPreviewProps {
  preview: FilePreview;
  onConfirm: () => void;
  onCancel: () => void;
  isImporting: boolean;
}

export default function ImportPreview({ preview, onConfirm, onCancel, isImporting }: ImportPreviewProps) {
  const formatFileSize = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const formatPrice = (price: number): string => {
    return price.toFixed(5);
  };

  return (
    <div className="bg-slate-800/50 rounded-lg p-6 backdrop-blur-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">Import Preview</h3>
        <div className="flex items-center space-x-2">
          <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
            {preview.symbol}
          </span>
        </div>
      </div>

      {/* File Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-slate-700/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-400 mb-1">Filename</h4>
          <p className="text-white font-mono text-sm break-all">{preview.filename}</p>
        </div>
        
        <div className="bg-slate-700/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-400 mb-1">Record Count</h4>
          <p className="text-2xl font-bold text-white">{formatNumber(preview.record_count)}</p>
        </div>
        
        <div className="bg-slate-700/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-400 mb-1">File Size</h4>
          <p className="text-2xl font-bold text-white">{formatFileSize(preview.file_size)}</p>
        </div>
        
        <div className="bg-slate-700/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-400 mb-1">Symbol ID</h4>
          <p className="text-2xl font-bold text-white">{preview.symbol_id}</p>
        </div>
      </div>

      {/* Date Range */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-slate-700/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-400 mb-1">Start Date</h4>
          <p className="text-white font-mono">{formatDate(preview.start_date)}</p>
        </div>
        
        <div className="bg-slate-700/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-400 mb-1">End Date</h4>
          <p className="text-white font-mono">{formatDate(preview.end_date)}</p>
        </div>
      </div>

      {/* Sample Data */}
      <div className="mb-6">
        <h4 className="text-lg font-medium text-white mb-4">Sample Data (First 5 Records)</h4>
        <div className="bg-slate-900/50 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="bg-slate-700/50">
                <tr>
                  <th className="text-left p-3 text-slate-300 font-medium">Time (UTC)</th>
                  <th className="text-right p-3 text-slate-300 font-medium">Ask</th>
                  <th className="text-right p-3 text-slate-300 font-medium">Bid</th>
                  <th className="text-right p-3 text-slate-300 font-medium">Ask Vol</th>
                  <th className="text-right p-3 text-slate-300 font-medium">Bid Vol</th>
                  <th className="text-right p-3 text-slate-300 font-medium">Spread</th>
                </tr>
              </thead>
              <tbody>
                {preview.sample_data.map((row, index) => (
                  <tr key={index} className="border-t border-slate-700/50">
                    <td className="p-3 text-white font-mono">{formatDate(row.utc)}</td>
                    <td className="p-3 text-white font-mono text-right">{formatPrice(row.ask)}</td>
                    <td className="p-3 text-white font-mono text-right">{formatPrice(row.bid)}</td>
                    <td className="p-3 text-slate-300 font-mono text-right">
                      {row.ask_vol ? formatNumber(row.ask_vol) : '-'}
                    </td>
                    <td className="p-3 text-slate-300 font-mono text-right">
                      {row.bid_vol ? formatNumber(row.bid_vol) : '-'}
                    </td>
                    <td className="p-3 text-slate-300 font-mono text-right">
                      {formatPrice(row.ask - row.bid)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Import Warning */}
      <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <div className="text-yellow-500 mt-0.5">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h5 className="text-yellow-400 font-medium mb-1">Import Confirmation Required</h5>
            <p className="text-yellow-200 text-sm">
              This will import {formatNumber(preview.record_count)} tick records for {preview.symbol}. 
              Duplicate records (same symbol and timestamp) will be automatically skipped. 
              Continuous aggregates will be refreshed after import.
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4">
        <button
          onClick={onCancel}
          disabled={isImporting}
          className={`px-6 py-2 rounded-lg transition-colors ${
            isImporting
              ? 'bg-slate-600 text-slate-400 cursor-not-allowed'
              : 'bg-slate-600 hover:bg-slate-500 text-white'
          }`}
        >
          Cancel
        </button>
        
        <button
          onClick={onConfirm}
          disabled={isImporting}
          className={`px-6 py-2 rounded-lg transition-colors ${
            isImporting
              ? 'bg-slate-600 text-slate-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700 text-white'
          }`}
        >
          {isImporting ? 'Importing...' : 'Confirm Import'}
        </button>
      </div>
    </div>
  );
}
