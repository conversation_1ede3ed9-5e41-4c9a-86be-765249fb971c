import type { Metadata } from "next";
import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Continuum Trading Platform",
  description: "Professional forex tick data management, strategy development, backtesting, and live trading platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <main className="flex-1">
          {children}
        </main>

        {/* Sticky Footer */}
        <footer className="bg-slate-900 border-t border-slate-700 py-4 mt-auto">
          <div className="container mx-auto px-4 text-center">
            <p className="text-slate-400 text-sm">
              Copyright 2025 Mark Smith
            </p>
          </div>
        </footer>
      </body>
    </html>
  );
}
