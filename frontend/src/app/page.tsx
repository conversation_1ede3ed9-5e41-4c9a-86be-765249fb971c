import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-12">
          <h1 className="text-5xl font-bold text-white mb-4">
            Continuum
          </h1>
          <p className="text-xl text-blue-200 mb-2">
            Trading Platform for Forex Tick Data Management
          </p>
          <p className="text-lg text-slate-300">
            Strategy Development • Backtesting • Live Trading
          </p>
        </header>

        {/* Phase Navigation */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">

          {/* Phase 1: Data Management - Active */}
          <Link href="/data-management" className="group">
            <div className="bg-gradient-to-br from-green-600 to-green-700 rounded-lg p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="text-white">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold">Phase 1</h3>
                  <span className="bg-green-500 text-green-100 px-2 py-1 rounded-full text-xs font-medium">
                    ACTIVE
                  </span>
                </div>
                <h4 className="text-lg font-medium mb-2">Data Management</h4>
                <p className="text-green-100 text-sm mb-4">
                  CSV tick data import and TimescaleDB management with real-time statistics
                </p>
                <ul className="text-green-100 text-xs space-y-1">
                  <li>• File Upload Interface</li>
                  <li>• Import Preview & Validation</li>
                  <li>• Database Statistics Dashboard</li>
                  <li>• Continuous Aggregate Refresh</li>
                </ul>
              </div>
            </div>
          </Link>

          {/* Phase 2: Strategy Backtesting - Coming Soon */}
          <div className="bg-gradient-to-br from-slate-600 to-slate-700 rounded-lg p-6 shadow-lg opacity-75">
            <div className="text-white">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold">Phase 2</h3>
                <span className="bg-slate-500 text-slate-100 px-2 py-1 rounded-full text-xs font-medium">
                  COMING SOON
                </span>
              </div>
              <h4 className="text-lg font-medium mb-2">Strategy Backtesting</h4>
              <p className="text-slate-300 text-sm mb-4">
                Visual strategy builder with historical data testing and performance analytics
              </p>
              <ul className="text-slate-300 text-xs space-y-1">
                <li>• Visual Strategy Builder</li>
                <li>• Historical Data Testing</li>
                <li>• Performance Analytics</li>
                <li>• Technical Indicators</li>
              </ul>
            </div>
          </div>

          {/* Phase 3: Production Testing - Coming Soon */}
          <div className="bg-gradient-to-br from-slate-600 to-slate-700 rounded-lg p-6 shadow-lg opacity-75">
            <div className="text-white">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold">Phase 3</h3>
                <span className="bg-slate-500 text-slate-100 px-2 py-1 rounded-full text-xs font-medium">
                  COMING SOON
                </span>
              </div>
              <h4 className="text-lg font-medium mb-2">Production Testing</h4>
              <p className="text-slate-300 text-sm mb-4">
                RabbitMQ-based strategy testing with simulated trading environment
              </p>
              <ul className="text-slate-300 text-xs space-y-1">
                <li>• Message-Driven Architecture</li>
                <li>• Simulated Trading Backend</li>
                <li>• Real-time Position Monitoring</li>
                <li>• Performance Analytics</li>
              </ul>
            </div>
          </div>

          {/* Phase 4: Live Demo Trading - Coming Soon */}
          <div className="bg-gradient-to-br from-slate-600 to-slate-700 rounded-lg p-6 shadow-lg opacity-75">
            <div className="text-white">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold">Phase 4</h3>
                <span className="bg-slate-500 text-slate-100 px-2 py-1 rounded-full text-xs font-medium">
                  COMING SOON
                </span>
              </div>
              <h4 className="text-lg font-medium mb-2">Live Demo Trading</h4>
              <p className="text-slate-300 text-sm mb-4">
                Real demo account integration with existing AMQP infrastructure
              </p>
              <ul className="text-slate-300 text-xs space-y-1">
                <li>• Live Demo Integration</li>
                <li>• Real Market Data</li>
                <li>• Risk Management</li>
                <li>• Performance Tracking</li>
              </ul>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="mt-12 max-w-4xl mx-auto">
          <div className="bg-slate-800/50 rounded-lg p-6 backdrop-blur-sm">
            <h3 className="text-xl font-semibold text-white mb-4">System Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-slate-300">Backend API</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-slate-300">TimescaleDB</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-slate-300">RabbitMQ</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
