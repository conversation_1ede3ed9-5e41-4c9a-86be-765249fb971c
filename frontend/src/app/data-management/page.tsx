'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import DataManagementWorkflow from '@/components/DataManagementWorkflow';
import DatabaseStatistics from '@/components/DatabaseStatistics';

export default function DataManagement() {
  const [activeTab, setActiveTab] = useState('statistics'); // Default to statistics tab

  // Check URL params for tab selection
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    if (tab === 'upload') {
      setActiveTab('upload');
    } else if (tab === 'statistics') {
      setActiveTab('statistics');
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Link href="/" className="text-blue-400 hover:text-blue-300 mb-2 inline-block">
                ← Back to Dashboard
              </Link>
              <h1 className="text-4xl font-bold text-white mb-2">
                Data Management System
              </h1>
              <p className="text-lg text-slate-300">
                Phase 1: CSV Tick Data Import and TimescaleDB Management
              </p>
            </div>
            <div className="text-right">
              <div className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                Phase 1 Active
              </div>
            </div>
          </div>
        </header>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-1 bg-slate-800/50 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('statistics')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'statistics'
                  ? 'bg-blue-600 text-white'
                  : 'text-slate-300 hover:text-white hover:bg-slate-700'
              }`}
            >
              Database Statistics
            </button>
            <button
              onClick={() => setActiveTab('upload')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'upload'
                  ? 'bg-blue-600 text-white'
                  : 'text-slate-300 hover:text-white hover:bg-slate-700'
              }`}
            >
              File Upload
            </button>
          </nav>
        </div>

        {/* Content Area */}
        <div className="bg-slate-800/30 rounded-lg backdrop-blur-sm">
          {activeTab === 'statistics' && (
            <div className="p-8">
              <DatabaseStatistics />
            </div>
          )}

          {activeTab === 'upload' && (
            <div className="p-8">
              <h2 className="text-2xl font-semibold text-white mb-6">
                CSV File Upload & Import
              </h2>

              <DataManagementWorkflow />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
