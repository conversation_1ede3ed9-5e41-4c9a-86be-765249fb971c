const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

// Types
export interface TickRow {
  utc: string;
  ask: number;
  bid: number;
  askvol?: number;
  bidvol?: number;
}

export interface FilePreview {
  filename: string;
  symbol: string;
  symbol_id: number;
  record_count: number;
  start_date: string;
  end_date: string;
  file_size: number;
  sample_data: TickRow[];
}

export interface UploadResponse {
  success: boolean;
  message: string;
  filename: string;
  temp_path: string;
  symbol: string;
  symbol_id: number;
  size: number;
}

export interface ImportResult {
  success: boolean;
  message: string;
  records_imported: number;
  records_skipped: number;
  duration: string;
  start_time: string;
  end_time: string;
}

export interface InstrumentStats {
  symbol: string;
  symbol_id: number;
  tick_count: number;
  start_date: string;
  end_date: string;
  min_ask: number;
  max_ask: number;
  min_bid: number;
  max_bid: number;
  avg_spread: number;
  last_updated: string;
}

export interface DatabasePerformance {
  chunk_count: number;
  compression_ratio: number;
  index_size: string;
  table_size: string;
  avg_query_time: string;
}

export interface ContinuousAggregateInfo {
  name: string;
  timeframe: string;
  bucket_count: number;
  last_refresh?: string;
  refresh_lag: string;
  compression_ratio: number;
}

export interface DatabaseStats {
  total_ticks: number;
  total_instruments: number;
  database_size: string;
  oldest_record: string;
  newest_record: string;
  instruments: InstrumentStats[];
  performance: DatabasePerformance;
  continuous_aggregates: ContinuousAggregateInfo[];
}

// API Functions
export class ApiService {
  private static async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  // Health check
  static async healthCheck(): Promise<{ status: string; service: string }> {
    return this.request('/api/health');
  }

  // File upload
  static async uploadFile(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/api/import/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Upload failed: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  // File preview
  static async previewFile(tempPath: string): Promise<FilePreview> {
    return this.request('/api/import/preview', {
      method: 'POST',
      body: JSON.stringify({ temp_path: tempPath }),
    });
  }

  // Execute import
  static async executeImport(tempPath: string, symbolId: number): Promise<ImportResult> {
    return this.request('/api/import/execute', {
      method: 'POST',
      body: JSON.stringify({
        temp_path: tempPath,
        symbol_id: symbolId,
      }),
    });
  }

  // Get database statistics
  static async getDatabaseStats(): Promise<DatabaseStats> {
    return this.request('/api/stats/database');
  }

  // Get instrument statistics
  static async getInstrumentStats(symbolId?: number): Promise<InstrumentStats[]> {
    const endpoint = symbolId 
      ? `/api/stats/instruments?symbol_id=${symbolId}`
      : '/api/stats/instruments';
    return this.request(endpoint);
  }
}

// Utility functions
export const formatFileSize = (bytes: number): string => {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
};

export const formatNumber = (num: number): string => {
  return num.toLocaleString();
};

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString();
};

export const formatPrice = (price: number): string => {
  return price.toFixed(5);
};

export const formatDuration = (durationString: string): string => {
  // Parse duration string like "1.234567s" or "2m30.123s"
  if (durationString.includes('m')) {
    return durationString; // Already formatted
  }
  
  const seconds = parseFloat(durationString.replace('s', ''));
  if (seconds < 1) {
    return `${(seconds * 1000).toFixed(0)}ms`;
  } else if (seconds < 60) {
    return `${seconds.toFixed(2)}s`;
  } else {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(2)}s`;
  }
};
