package models

import (
	"time"
)

// DatabaseStats represents overall database statistics
type DatabaseStats struct {
	TotalTicks       int64                     `json:"total_ticks"`
	TotalInstruments int                       `json:"total_instruments"`
	DatabaseSize     string                    `json:"database_size"`
	OldestRecord     *time.Time                `json:"oldest_record"`
	NewestRecord     *time.Time                `json:"newest_record"`
	Instruments      []InstrumentStats         `json:"instruments"`
	Performance      DatabasePerformance       `json:"performance"`
	ContinuousAggs   []ContinuousAggregateInfo `json:"continuous_aggregates"`
}

// InstrumentStats represents statistics for a specific instrument
type InstrumentStats struct {
	Symbol       string     `json:"symbol"`
	SymbolID     int        `json:"symbol_id"`
	TickCount    int64      `json:"tick_count"`
	StartDate    *time.Time `json:"start_date"`
	EndDate      *time.Time `json:"end_date"`
	MinAsk       *float64   `json:"min_ask"`
	MaxAsk       *float64   `json:"max_ask"`
	MinBid       *float64   `json:"min_bid"`
	MaxBid       *float64   `json:"max_bid"`
	AvgSpread    *float64   `json:"avg_spread"`
	MinSpread    *float64   `json:"min_spread"`
	MaxSpread    *float64   `json:"max_spread"`
	MedianSpread *float64   `json:"median_spread"`
	LastUpdated  *time.Time `json:"last_updated"`
}

// DatabasePerformance represents database performance metrics
type DatabasePerformance struct {
	ChunkCount       int     `json:"chunk_count"`
	CompressionRatio float64 `json:"compression_ratio"`
	IndexSize        string  `json:"index_size"`
	TableSize        string  `json:"table_size"`
	AvgQueryTime     string  `json:"avg_query_time"`
}

// ContinuousAggregateInfo represents information about continuous aggregates
type ContinuousAggregateInfo struct {
	Name             string     `json:"name"`
	Timeframe        string     `json:"timeframe"`
	BucketCount      int64      `json:"bucket_count"`
	LastRefresh      *time.Time `json:"last_refresh"`
	RefreshLag       string     `json:"refresh_lag"`
	CompressionRatio float64    `json:"compression_ratio"`
}

// TimeframeStats represents statistics for different OHLC timeframes
type TimeframeStats struct {
	Timeframe   string `json:"timeframe"`
	BucketCount int64  `json:"bucket_count"`
	QueryTime   string `json:"query_time"`
	Compression string `json:"compression"`
}
