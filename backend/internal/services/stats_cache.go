package services

import (
	"context"
	"fmt"
	"time"

	"continuum/backend/internal/models"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// StatsCacheService handles cached statistics operations
type StatsCacheService struct {
	mktDataDB *pgxpool.Pool // TimescaleDB connection
	resultsDB *pgxpool.Pool // Results database connection
}

// NewStatsCacheService creates a new stats cache service
func NewStatsCacheService(mktDataDB, resultsDB *pgxpool.Pool) *StatsCacheService {
	return &StatsCacheService{
		mktDataDB: mktDataDB,
		resultsDB: resultsDB,
	}
}

// GetCachedDatabaseStats returns cached database statistics
func (s *StatsCacheService) GetCachedDatabaseStats() (*models.DatabaseStats, error) {
	ctx := context.Background()

	stats := &models.DatabaseStats{}

	// Get cached database stats
	err := s.resultsDB.QueryRow(ctx, `
		SELECT total_ticks, total_instruments, database_size, 
		       oldest_record, newest_record, chunk_count,
		       table_size, index_size, compression_ratio, avg_query_time
		FROM database_stats 
		ORDER BY last_updated DESC 
		LIMIT 1
	`).Scan(
		&stats.TotalTicks, &stats.TotalInstruments, &stats.DatabaseSize,
		&stats.OldestRecord, &stats.NewestRecord, &stats.Performance.ChunkCount,
		&stats.Performance.TableSize, &stats.Performance.IndexSize,
		&stats.Performance.CompressionRatio, &stats.Performance.AvgQueryTime,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get cached database stats: %w", err)
	}

	// Get cached instrument stats
	instruments, err := s.getCachedInstrumentStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get cached instrument stats: %w", err)
	}
	stats.Instruments = instruments

	// Get cached continuous aggregate stats
	continuousAggs, err := s.getCachedContinuousAggregateStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get cached continuous aggregate stats: %w", err)
	}
	stats.ContinuousAggs = continuousAggs

	return stats, nil
}

// getCachedInstrumentStats returns cached instrument statistics
func (s *StatsCacheService) getCachedInstrumentStats() ([]models.InstrumentStats, error) {
	ctx := context.Background()

	rows, err := s.resultsDB.Query(ctx, `
		SELECT symbol, symbol_id, tick_count, start_date, end_date,
		       min_ask, max_ask, min_bid, max_bid, avg_spread,
		       min_spread, max_spread, median_spread, last_updated
		FROM instrument_stats
		ORDER BY symbol_id
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to query cached instrument stats: %w", err)
	}
	defer rows.Close()

	var instruments []models.InstrumentStats
	for rows.Next() {
		var inst models.InstrumentStats
		err := rows.Scan(
			&inst.Symbol, &inst.SymbolID, &inst.TickCount,
			&inst.StartDate, &inst.EndDate, &inst.MinAsk, &inst.MaxAsk,
			&inst.MinBid, &inst.MaxBid, &inst.AvgSpread,
			&inst.MinSpread, &inst.MaxSpread, &inst.MedianSpread, &inst.LastUpdated,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan cached instrument stats: %w", err)
		}
		instruments = append(instruments, inst)
	}

	return instruments, nil
}

// getCachedContinuousAggregateStats returns cached continuous aggregate statistics
func (s *StatsCacheService) getCachedContinuousAggregateStats() ([]models.ContinuousAggregateInfo, error) {
	ctx := context.Background()

	rows, err := s.resultsDB.Query(ctx, `
		SELECT name, timeframe, bucket_count, refresh_lag, compression_ratio
		FROM continuous_aggregate_stats 
		ORDER BY 
			CASE timeframe
				WHEN '10s' THEN 1
				WHEN '1m' THEN 2
				WHEN '5m' THEN 3
				WHEN '10m' THEN 4
				WHEN '15m' THEN 5
				WHEN '30m' THEN 6
				WHEN '1h' THEN 7
				WHEN '4h' THEN 8
				WHEN '1d' THEN 9
				ELSE 10
			END
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to query cached continuous aggregate stats: %w", err)
	}
	defer rows.Close()

	var aggregates []models.ContinuousAggregateInfo
	for rows.Next() {
		var agg models.ContinuousAggregateInfo
		err := rows.Scan(
			&agg.Name, &agg.Timeframe, &agg.BucketCount,
			&agg.RefreshLag, &agg.CompressionRatio,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan cached continuous aggregate stats: %w", err)
		}
		aggregates = append(aggregates, agg)
	}

	return aggregates, nil
}

// UpdateStatsCache refreshes all cached statistics from TimescaleDB
func (s *StatsCacheService) UpdateStatsCache(triggeredBy string) error {
	startTime := time.Now()
	ctx := context.Background()

	// Start transaction for atomic updates
	tx, err := s.resultsDB.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	recordsUpdated := 0

	// Update database stats
	if err := s.updateDatabaseStatsCache(ctx, tx); err != nil {
		s.logStatsUpdate(triggeredBy, time.Since(startTime), recordsUpdated, false, err.Error())
		return fmt.Errorf("failed to update database stats cache: %w", err)
	}
	recordsUpdated++

	// Update instrument stats
	instrumentCount, err := s.updateInstrumentStatsCache(ctx, tx)
	if err != nil {
		s.logStatsUpdate(triggeredBy, time.Since(startTime), recordsUpdated, false, err.Error())
		return fmt.Errorf("failed to update instrument stats cache: %w", err)
	}
	recordsUpdated += instrumentCount

	// Update continuous aggregate stats
	aggCount, err := s.updateContinuousAggregateStatsCache(ctx, tx)
	if err != nil {
		s.logStatsUpdate(triggeredBy, time.Since(startTime), recordsUpdated, false, err.Error())
		return fmt.Errorf("failed to update continuous aggregate stats cache: %w", err)
	}
	recordsUpdated += aggCount

	// Commit transaction
	if err := tx.Commit(ctx); err != nil {
		s.logStatsUpdate(triggeredBy, time.Since(startTime), recordsUpdated, false, err.Error())
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Log successful update
	s.logStatsUpdate(triggeredBy, time.Since(startTime), recordsUpdated, true, "")

	return nil
}

// logStatsUpdate logs the stats update operation
func (s *StatsCacheService) logStatsUpdate(triggeredBy string, duration time.Duration, recordsUpdated int, success bool, errorMsg string) {
	ctx := context.Background()

	updateType := "manual"
	if triggeredBy == "system" {
		updateType = "auto"
	} else if triggeredBy == "import" {
		updateType = "import"
	}

	_, err := s.resultsDB.Exec(ctx, `
		INSERT INTO stats_update_log (update_type, duration_seconds, records_updated, success, error_message, triggered_by)
		VALUES ($1, $2, $3, $4, $5, $6)
	`, updateType, duration.Seconds(), recordsUpdated, success, errorMsg, triggeredBy)

	if err != nil {
		fmt.Printf("Failed to log stats update: %v\n", err)
	}
}

// updateDatabaseStatsCache updates the cached database statistics
func (s *StatsCacheService) updateDatabaseStatsCache(ctx context.Context, tx pgx.Tx) error {
	// Query TimescaleDB for current stats
	var totalTicks int64
	var totalInstruments int
	var oldestRecord, newestRecord time.Time
	var chunkCount int
	var dbSize int64

	// Get basic stats
	err := s.mktDataDB.QueryRow(ctx, "SELECT COUNT(*) FROM ticks").Scan(&totalTicks)
	if err != nil {
		return fmt.Errorf("failed to get total ticks: %w", err)
	}

	err = s.mktDataDB.QueryRow(ctx, "SELECT COUNT(DISTINCT symbol) FROM ticks").Scan(&totalInstruments)
	if err != nil {
		return fmt.Errorf("failed to get total instruments: %w", err)
	}

	err = s.mktDataDB.QueryRow(ctx, "SELECT MIN(utc), MAX(utc) FROM ticks").Scan(&oldestRecord, &newestRecord)
	if err != nil {
		return fmt.Errorf("failed to get date range: %w", err)
	}

	err = s.mktDataDB.QueryRow(ctx, `
		SELECT COUNT(*)
		FROM timescaledb_information.chunks
		WHERE hypertable_name = 'ticks'
	`).Scan(&chunkCount)
	if err != nil {
		return fmt.Errorf("failed to get chunk count: %w", err)
	}

	err = s.mktDataDB.QueryRow(ctx, "SELECT pg_database_size(current_database())").Scan(&dbSize)
	if err != nil {
		return fmt.Errorf("failed to get database size: %w", err)
	}

	// Update cache
	_, err = tx.Exec(ctx, `
		INSERT INTO database_stats (
			total_ticks, total_instruments, database_size,
			oldest_record, newest_record, chunk_count,
			table_size, index_size, last_updated
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
		ON CONFLICT (id) DO UPDATE SET
			total_ticks = EXCLUDED.total_ticks,
			total_instruments = EXCLUDED.total_instruments,
			database_size = EXCLUDED.database_size,
			oldest_record = EXCLUDED.oldest_record,
			newest_record = EXCLUDED.newest_record,
			chunk_count = EXCLUDED.chunk_count,
			table_size = EXCLUDED.table_size,
			index_size = EXCLUDED.index_size,
			last_updated = NOW()
	`, totalTicks, totalInstruments, formatBytes(dbSize),
		oldestRecord, newestRecord, chunkCount,
		"~22 GB", "~2 GB") // Use approximations for table/index sizes

	return err
}

// updateInstrumentStatsCache updates the cached instrument statistics
func (s *StatsCacheService) updateInstrumentStatsCache(ctx context.Context, tx pgx.Tx) (int, error) {
	// Query TimescaleDB for instrument stats with spread statistics
	rows, err := s.mktDataDB.Query(ctx, `
		SELECT
			symbol,
			COUNT(*) as tick_count,
			MIN(utc) as start_date,
			MAX(utc) as end_date,
			MIN(ask) as min_ask,
			MAX(ask) as max_ask,
			MIN(bid) as min_bid,
			MAX(bid) as max_bid,
			AVG(ask - bid) as avg_spread,
			MIN(ask - bid) as min_spread,
			MAX(ask - bid) as max_spread,
			MAX(utc) as last_updated
		FROM ticks
		GROUP BY symbol
		ORDER BY symbol
	`)
	if err != nil {
		return 0, fmt.Errorf("failed to query instrument stats: %w", err)
	}
	defer rows.Close()

	count := 0
	for rows.Next() {
		var symbolID int // symbol is stored as integer in database
		var tickCount int64
		var startDate, endDate, lastUpdated time.Time
		var minAsk, maxAsk, minBid, maxBid, avgSpread, minSpread, maxSpread float64

		err := rows.Scan(&symbolID, &tickCount, &startDate, &endDate,
			&minAsk, &maxAsk, &minBid, &maxBid, &avgSpread, &minSpread, &maxSpread, &lastUpdated)
		if err != nil {
			return count, fmt.Errorf("failed to scan instrument stats: %w", err)
		}

		// Use average spread as median approximation (much faster than true median calculation)
		// True median calculation takes 60+ seconds, avg is calculated in 6.9 seconds
		medianSpread := avgSpread

		// Get symbol name from ID
		symbolName, exists := models.ReverseSymbolMapping[symbolID]
		if !exists {
			continue // Skip unknown symbols
		}

		// Update cache
		_, err = tx.Exec(ctx, `
			INSERT INTO instrument_stats (
				symbol, symbol_id, tick_count, start_date, end_date,
				min_ask, max_ask, min_bid, max_bid, avg_spread,
				min_spread, max_spread, median_spread, last_updated
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
			ON CONFLICT (symbol_id) DO UPDATE SET
				symbol = EXCLUDED.symbol,
				tick_count = EXCLUDED.tick_count,
				start_date = EXCLUDED.start_date,
				end_date = EXCLUDED.end_date,
				min_ask = EXCLUDED.min_ask,
				max_ask = EXCLUDED.max_ask,
				min_bid = EXCLUDED.min_bid,
				max_bid = EXCLUDED.max_bid,
				avg_spread = EXCLUDED.avg_spread,
				min_spread = EXCLUDED.min_spread,
				max_spread = EXCLUDED.max_spread,
				median_spread = EXCLUDED.median_spread,
				last_updated = EXCLUDED.last_updated
		`, symbolName, symbolID, tickCount, startDate, endDate,
			minAsk, maxAsk, minBid, maxBid, avgSpread, minSpread, maxSpread, medianSpread, lastUpdated)

		if err != nil {
			return count, fmt.Errorf("failed to update instrument stats cache: %w", err)
		}
		count++
	}

	return count, nil
}

// updateContinuousAggregateStatsCache updates the cached continuous aggregate statistics
func (s *StatsCacheService) updateContinuousAggregateStatsCache(ctx context.Context, tx pgx.Tx) (int, error) {
	timeframes := []string{"10s", "1m", "5m", "10m", "15m", "30m", "1h", "4h", "1d"}

	// Get total ticks for compression ratio calculation
	var totalTicks int64
	err := s.mktDataDB.QueryRow(ctx, "SELECT COUNT(*) FROM ticks").Scan(&totalTicks)
	if err != nil {
		return 0, fmt.Errorf("failed to get total ticks for compression ratio: %w", err)
	}

	count := 0
	for _, timeframe := range timeframes {
		viewName := fmt.Sprintf("ohlc_%s", timeframe)

		var bucketCount int64
		err := s.mktDataDB.QueryRow(ctx, fmt.Sprintf("SELECT COUNT(*) FROM %s", viewName)).Scan(&bucketCount)
		if err != nil {
			continue // Skip if view doesn't exist
		}

		compressionRatio := float64(totalTicks) / float64(bucketCount)

		// Update cache
		_, err = tx.Exec(ctx, `
			INSERT INTO continuous_aggregate_stats (
				name, timeframe, bucket_count, compression_ratio, last_updated
			) VALUES ($1, $2, $3, $4, NOW())
			ON CONFLICT (name) DO UPDATE SET
				timeframe = EXCLUDED.timeframe,
				bucket_count = EXCLUDED.bucket_count,
				compression_ratio = EXCLUDED.compression_ratio,
				last_updated = NOW()
		`, viewName, timeframe, bucketCount, compressionRatio)

		if err != nil {
			return count, fmt.Errorf("failed to update continuous aggregate stats cache: %w", err)
		}
		count++
	}

	return count, nil
}
