package services

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"continuum/backend/internal/models"

	"github.com/jackc/pgx/v5/pgxpool"
)

// ImportService handles CSV file import operations
type ImportService struct {
	db *pgxpool.Pool
}

// NewImportService creates a new import service
func NewImportService(db *pgxpool.Pool) *ImportService {
	return &ImportService{db: db}
}

// PreviewFile analyzes a CSV file and returns metadata without importing
func (s *ImportService) PreviewFile(filePath string) (*models.FilePreview, error) {
	// Extract symbol from filename
	filename := filepath.Base(filePath)
	symbol, symbolID, ok := models.GetSymbolFromFilename(filename)
	if !ok {
		return nil, fmt.Errorf("unable to extract symbol from filename: %s", filename)
	}

	return s.PreviewFileWithSymbol(filePath, filename, symbol, symbolID)
}

// PreviewFileWithSymbol analyzes a CSV file with provided symbol information
func (s *ImportService) PreviewFileWithSymbol(filePath, originalFilename, symbol string, symbolID int) (*models.FilePreview, error) {

	// Get file info
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// Open and parse CSV file
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// Read header
	header, err := reader.Read()
	if err != nil {
		return nil, fmt.Errorf("failed to read CSV header: %w", err)
	}

	// Validate header format
	if err := s.validateHeader(header); err != nil {
		return nil, fmt.Errorf("invalid CSV header: %w", err)
	}

	var recordCount int
	var startDate, endDate time.Time
	var sampleData []models.TickRow
	const maxSamples = 5

	// Process records
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("error reading CSV record: %w", err)
		}

		recordCount++

		// Parse the record
		tickRow, err := s.parseTickRow(record)
		if err != nil {
			return nil, fmt.Errorf("error parsing record %d: %w", recordCount, err)
		}

		// Track date range
		if recordCount == 1 {
			startDate = tickRow.UTC
			endDate = tickRow.UTC
		} else {
			if tickRow.UTC.Before(startDate) {
				startDate = tickRow.UTC
			}
			if tickRow.UTC.After(endDate) {
				endDate = tickRow.UTC
			}
		}

		// Collect sample data
		if len(sampleData) < maxSamples {
			sampleData = append(sampleData, *tickRow)
		}
	}

	if recordCount == 0 {
		return nil, fmt.Errorf("CSV file is empty")
	}

	return &models.FilePreview{
		Filename:    originalFilename,
		Symbol:      symbol,
		SymbolID:    symbolID,
		RecordCount: recordCount,
		StartDate:   startDate,
		EndDate:     endDate,
		FileSize:    fileInfo.Size(),
		SampleData:  sampleData,
	}, nil
}

// ExecuteImport imports a CSV file into the database
func (s *ImportService) ExecuteImport(filePath string, symbolID int) (*models.ImportResult, error) {
	startTime := time.Now()

	// Get symbol name from ID for validation
	symbolName, exists := models.ReverseSymbolMapping[symbolID]
	if !exists {
		return nil, fmt.Errorf("invalid symbol ID: %d", symbolID)
	}

	// Validate file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file does not exist: %s", filePath)
	}

	// Get record count from file for calculating skipped records
	recordCount, err := s.getFileRecordCount(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to count records: %w", err)
	}

	// Execute the import using the existing stored procedure
	var recordsImported int
	err = s.db.QueryRow(context.Background(),
		"SELECT import_eurusd_file($1)", filePath).Scan(&recordsImported)
	if err != nil {
		return nil, fmt.Errorf("database import failed: %w", err)
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// Calculate skipped records
	recordsSkipped := recordCount - recordsImported

	return &models.ImportResult{
		Success:         true,
		Message:         fmt.Sprintf("Successfully imported %d records for %s", recordsImported, symbolName),
		RecordsImported: recordsImported,
		RecordsSkipped:  recordsSkipped,
		Duration:        duration.String(),
		StartTime:       startTime,
		EndTime:         endTime,
	}, nil
}

// getFileRecordCount counts the number of data records in a CSV file (excluding header)
func (s *ImportService) getFileRecordCount(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// Skip header
	_, err = reader.Read()
	if err != nil {
		return 0, fmt.Errorf("failed to read header: %w", err)
	}

	// Count data records
	count := 0
	for {
		_, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return 0, fmt.Errorf("error reading record %d: %w", count+1, err)
		}
		count++
	}

	return count, nil
}

// validateHeader checks if the CSV header matches expected format
func (s *ImportService) validateHeader(header []string) error {
	if len(header) != 5 {
		return fmt.Errorf("expected 5 columns, got %d", len(header))
	}

	// Check each column with flexible matching
	expectedColumns := map[int][]string{
		0: {"Time (UTC)", "Time(UTC)", "UTC", "Timestamp"},
		1: {"Ask"},
		2: {"Bid"},
		3: {"AskVol", "AskVolume", "Ask Vol", "Ask Volume"},
		4: {"BidVol", "BidVolume", "Bid Vol", "Bid Volume"},
	}

	for i, actualCol := range header {
		actualCol = strings.TrimSpace(actualCol)
		validOptions := expectedColumns[i]

		found := false
		for _, validCol := range validOptions {
			if actualCol == validCol {
				found = true
				break
			}
		}

		if !found {
			return fmt.Errorf("column %d: expected one of %v, got '%s'", i+1, validOptions, actualCol)
		}
	}

	return nil
}

// parseTickRow parses a CSV record into a TickRow
func (s *ImportService) parseTickRow(record []string) (*models.TickRow, error) {
	if len(record) != 5 {
		return nil, fmt.Errorf("expected 5 columns, got %d", len(record))
	}

	// Parse timestamp - try multiple formats
	timestampStr := strings.TrimSpace(record[0])
	var utc time.Time
	var err error

	// Try different timestamp formats
	timestampFormats := []string{
		"2006-01-02 15:04:05.000",  // Original format: 2025-01-15 09:30:00.123
		"2006.01.02 15:04:05.000",  // Dot format: 2025.01.15 09:30:00.123
		"2006-01-02 15:04:05",      // Without milliseconds: 2025-01-15 09:30:00
		"2006.01.02 15:04:05",      // Dot format without milliseconds: 2025.01.15 09:30:00
		"2006-01-02T15:04:05.000Z", // ISO format with Z
		"2006-01-02T15:04:05Z",     // ISO format without milliseconds
	}

	for _, format := range timestampFormats {
		utc, err = time.Parse(format, timestampStr)
		if err == nil {
			break // Successfully parsed
		}
	}

	if err != nil {
		return nil, fmt.Errorf("invalid timestamp format '%s': tried formats %v", timestampStr, timestampFormats)
	}

	// Parse Ask
	ask, err := strconv.ParseFloat(strings.TrimSpace(record[1]), 64)
	if err != nil {
		return nil, fmt.Errorf("invalid ask price: %w", err)
	}

	// Parse Bid
	bid, err := strconv.ParseFloat(strings.TrimSpace(record[2]), 64)
	if err != nil {
		return nil, fmt.Errorf("invalid bid price: %w", err)
	}

	tickRow := &models.TickRow{
		UTC: utc,
		Ask: ask,
		Bid: bid,
	}

	// Parse AskVol (optional)
	if askVolStr := strings.TrimSpace(record[3]); askVolStr != "" {
		askVol, err := strconv.ParseFloat(askVolStr, 32)
		if err != nil {
			return nil, fmt.Errorf("invalid ask volume: %w", err)
		}
		askVolFloat32 := float32(askVol)
		tickRow.AskVol = &askVolFloat32
	}

	// Parse BidVol (optional)
	if bidVolStr := strings.TrimSpace(record[4]); bidVolStr != "" {
		bidVol, err := strconv.ParseFloat(bidVolStr, 32)
		if err != nil {
			return nil, fmt.Errorf("invalid bid volume: %w", err)
		}
		bidVolFloat32 := float32(bidVol)
		tickRow.BidVol = &bidVolFloat32
	}

	return tickRow, nil
}
