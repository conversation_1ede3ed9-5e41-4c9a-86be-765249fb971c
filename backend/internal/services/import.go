package services

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"continuum/backend/internal/models"

	"github.com/jackc/pgx/v5/pgxpool"
)

// ImportService handles CSV file import operations
type ImportService struct {
	db *pgxpool.Pool
}

// NewImportService creates a new import service
func NewImportService(db *pgxpool.Pool) *ImportService {
	return &ImportService{db: db}
}

// PreviewFile analyzes a CSV file and returns metadata without importing
func (s *ImportService) PreviewFile(filePath string) (*models.FilePreview, error) {
	// Extract symbol from filename
	filename := filepath.Base(filePath)
	symbol, symbolID, ok := models.GetSymbolFromFilename(filename)
	if !ok {
		return nil, fmt.Errorf("unable to extract symbol from filename: %s", filename)
	}

	return s.PreviewFileWithSymbol(filePath, filename, symbol, symbolID)
}

// PreviewFileWithSymbol analyzes a CSV file with provided symbol information
func (s *ImportService) PreviewFileWithSymbol(filePath, originalFilename, symbol string, symbolID int) (*models.FilePreview, error) {

	// Get file info
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// Open and parse CSV file
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// Read header
	header, err := reader.Read()
	if err != nil {
		return nil, fmt.Errorf("failed to read CSV header: %w", err)
	}

	// Validate header format
	if err := s.validateHeader(header); err != nil {
		return nil, fmt.Errorf("invalid CSV header: %w", err)
	}

	// Build column mapping
	columnMap, err := s.buildColumnMapping(header)
	if err != nil {
		return nil, fmt.Errorf("failed to build column mapping: %w", err)
	}

	var recordCount int
	var startDate, endDate time.Time
	var sampleData []models.TickRow
	const maxSamples = 5

	// Process records
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("error reading CSV record: %w", err)
		}

		recordCount++

		// Parse the record
		tickRow, err := s.parseTickRow(record, columnMap)
		if err != nil {
			return nil, fmt.Errorf("error parsing record %d: %w", recordCount, err)
		}

		// Track date range
		if recordCount == 1 {
			startDate = tickRow.UTC
			endDate = tickRow.UTC
		} else {
			if tickRow.UTC.Before(startDate) {
				startDate = tickRow.UTC
			}
			if tickRow.UTC.After(endDate) {
				endDate = tickRow.UTC
			}
		}

		// Collect sample data
		if len(sampleData) < maxSamples {
			sampleData = append(sampleData, *tickRow)
		}
	}

	if recordCount == 0 {
		return nil, fmt.Errorf("CSV file is empty")
	}

	return &models.FilePreview{
		Filename:    originalFilename,
		Symbol:      symbol,
		SymbolID:    symbolID,
		RecordCount: recordCount,
		StartDate:   startDate,
		EndDate:     endDate,
		FileSize:    fileInfo.Size(),
		SampleData:  sampleData,
	}, nil
}

// ExecuteImport imports a CSV file into the database
func (s *ImportService) ExecuteImport(filePath string, symbolID int) (*models.ImportResult, error) {
	startTime := time.Now()

	// Get symbol name from ID for validation
	symbolName, exists := models.ReverseSymbolMapping[symbolID]
	if !exists {
		return nil, fmt.Errorf("invalid symbol ID: %d", symbolID)
	}

	// Validate file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file does not exist: %s", filePath)
	}

	// Get record count from file for calculating skipped records
	recordCount, err := s.getFileRecordCount(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to count records: %w", err)
	}

	// Execute the import by reading CSV and inserting data directly
	recordsImported, err := s.importCSVData(filePath, symbolID)
	if err != nil {
		return nil, fmt.Errorf("database import failed: %w", err)
	}

	// Refresh continuous aggregates if records were imported
	if recordsImported > 0 {
		err = s.refreshContinuousAggregates()
		if err != nil {
			// Log warning but don't fail the import
			fmt.Printf("Warning: Failed to refresh continuous aggregates: %v\n", err)
		}
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// Calculate skipped records
	recordsSkipped := recordCount - recordsImported

	return &models.ImportResult{
		Success:         true,
		Message:         fmt.Sprintf("Successfully imported %d records for %s", recordsImported, symbolName),
		RecordsImported: recordsImported,
		RecordsSkipped:  recordsSkipped,
		Duration:        duration.String(),
		StartTime:       startTime,
		EndTime:         endTime,
	}, nil
}

// buildColumnMapping creates a mapping from standard column names to CSV column indices
func (s *ImportService) buildColumnMapping(header []string) (map[string]int, error) {
	columnMap := make(map[string]int)

	for i, col := range header {
		col = strings.TrimSpace(col)

		// Map time columns
		if col == "Time (UTC)" || col == "Time(UTC)" || col == "UTC" || col == "Timestamp" {
			columnMap["time"] = i
		}
		// Map ask column
		if col == "Ask" {
			columnMap["ask"] = i
		}
		// Map bid column
		if col == "Bid" {
			columnMap["bid"] = i
		}
		// Map ask volume columns
		if col == "AskVol" || col == "AskVolume" || col == "Ask Vol" || col == "Ask Volume" {
			columnMap["askVol"] = i
		}
		// Map bid volume columns
		if col == "BidVol" || col == "BidVolume" || col == "Bid Vol" || col == "Bid Volume" {
			columnMap["bidVol"] = i
		}
	}

	// Validate all required columns are present
	requiredColumns := []string{"time", "ask", "bid", "askVol", "bidVol"}
	for _, required := range requiredColumns {
		if _, exists := columnMap[required]; !exists {
			return nil, fmt.Errorf("required column type '%s' not found in header: %v", required, header)
		}
	}

	return columnMap, nil
}

// getFileRecordCount counts the number of data records in a CSV file (excluding header)
func (s *ImportService) getFileRecordCount(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// Skip header
	_, err = reader.Read()
	if err != nil {
		return 0, fmt.Errorf("failed to read header: %w", err)
	}

	// Count data records
	count := 0
	for {
		_, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return 0, fmt.Errorf("error reading record %d: %w", count+1, err)
		}
		count++
	}

	return count, nil
}

// validateHeader checks if the CSV header matches expected format
func (s *ImportService) validateHeader(header []string) error {
	if len(header) != 5 {
		return fmt.Errorf("expected 5 columns, got %d", len(header))
	}

	// Check each column with flexible matching
	expectedColumns := map[int][]string{
		0: {"Time (UTC)", "Time(UTC)", "UTC", "Timestamp"},
		1: {"Ask"},
		2: {"Bid"},
		3: {"AskVol", "AskVolume", "Ask Vol", "Ask Volume"},
		4: {"BidVol", "BidVolume", "Bid Vol", "Bid Volume"},
	}

	for i, actualCol := range header {
		actualCol = strings.TrimSpace(actualCol)
		validOptions := expectedColumns[i]

		found := false
		for _, validCol := range validOptions {
			if actualCol == validCol {
				found = true
				break
			}
		}

		if !found {
			return fmt.Errorf("column %d: expected one of %v, got '%s'", i+1, validOptions, actualCol)
		}
	}

	return nil
}

// parseTickRow parses a CSV record into a TickRow using column mapping
func (s *ImportService) parseTickRow(record []string, columnMap map[string]int) (*models.TickRow, error) {
	if len(record) != 5 {
		return nil, fmt.Errorf("expected 5 columns, got %d", len(record))
	}

	// Validate all required columns are present and have data
	requiredColumns := []string{"time", "ask", "bid", "askVol", "bidVol"}
	for _, col := range requiredColumns {
		colIndex, exists := columnMap[col]
		if !exists {
			return nil, fmt.Errorf("required column '%s' not found in CSV", col)
		}
		if colIndex >= len(record) {
			return nil, fmt.Errorf("column index %d out of range for record with %d columns", colIndex, len(record))
		}
		if strings.TrimSpace(record[colIndex]) == "" {
			return nil, fmt.Errorf("required column '%s' has empty value", col)
		}
	}

	// Parse timestamp using column mapping
	timestampStr := strings.TrimSpace(record[columnMap["time"]])
	var utc time.Time
	var err error

	// Try different timestamp formats
	timestampFormats := []string{
		"2006-01-02 15:04:05.000",  // Original format: 2025-01-15 09:30:00.123
		"2006.01.02 15:04:05.000",  // Dot format: 2025.01.15 09:30:00.123
		"2006-01-02 15:04:05",      // Without milliseconds: 2025-01-15 09:30:00
		"2006.01.02 15:04:05",      // Dot format without milliseconds: 2025.01.15 09:30:00
		"2006-01-02T15:04:05.000Z", // ISO format with Z
		"2006-01-02T15:04:05Z",     // ISO format without milliseconds
	}

	for _, format := range timestampFormats {
		utc, err = time.Parse(format, timestampStr)
		if err == nil {
			break // Successfully parsed
		}
	}

	if err != nil {
		return nil, fmt.Errorf("invalid timestamp format '%s': tried formats %v", timestampStr, timestampFormats)
	}

	// Parse Ask using column mapping
	ask, err := strconv.ParseFloat(strings.TrimSpace(record[columnMap["ask"]]), 64)
	if err != nil {
		return nil, fmt.Errorf("invalid ask price: %w", err)
	}

	// Parse Bid using column mapping
	bid, err := strconv.ParseFloat(strings.TrimSpace(record[columnMap["bid"]]), 64)
	if err != nil {
		return nil, fmt.Errorf("invalid bid price: %w", err)
	}

	// Parse AskVol using column mapping (required)
	askVol, err := strconv.ParseFloat(strings.TrimSpace(record[columnMap["askVol"]]), 32)
	if err != nil {
		return nil, fmt.Errorf("invalid ask volume: %w", err)
	}
	askVolFloat32 := float32(askVol)

	// Parse BidVol using column mapping (required)
	bidVol, err := strconv.ParseFloat(strings.TrimSpace(record[columnMap["bidVol"]]), 32)
	if err != nil {
		return nil, fmt.Errorf("invalid bid volume: %w", err)
	}
	bidVolFloat32 := float32(bidVol)

	return &models.TickRow{
		UTC:    utc,
		Ask:    ask,
		Bid:    bid,
		AskVol: &askVolFloat32,
		BidVol: &bidVolFloat32,
	}, nil
}

// importCSVData reads a CSV file and imports data directly into the database
func (s *ImportService) importCSVData(filePath string, symbolID int) (int, error) {
	// Open CSV file
	file, err := os.Open(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// Read header
	header, err := reader.Read()
	if err != nil {
		return 0, fmt.Errorf("failed to read CSV header: %w", err)
	}

	// Create column mapping
	columnMap, err := s.buildColumnMapping(header)
	if err != nil {
		return 0, fmt.Errorf("invalid CSV format: %w", err)
	}

	// Prepare batch insert statement
	const batchSize = 1000
	var batch [][]interface{}
	recordsImported := 0

	// Process CSV rows
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return recordsImported, fmt.Errorf("error reading CSV row: %w", err)
		}

		// Parse row data
		tickData, err := s.parseTickRow(record, columnMap)
		if err != nil {
			continue // Skip invalid rows
		}

		// Add to batch
		batch = append(batch, []interface{}{
			tickData.UTC,
			symbolID,
			tickData.Ask,
			tickData.Bid,
			tickData.AskVol,
			tickData.BidVol,
		})

		// Execute batch when full
		if len(batch) >= batchSize {
			imported, err := s.executeBatch(batch)
			if err != nil {
				return recordsImported, fmt.Errorf("batch insert failed: %w", err)
			}
			recordsImported += imported
			batch = batch[:0] // Clear batch
		}
	}

	// Execute remaining batch
	if len(batch) > 0 {
		imported, err := s.executeBatch(batch)
		if err != nil {
			return recordsImported, fmt.Errorf("final batch insert failed: %w", err)
		}
		recordsImported += imported
	}

	return recordsImported, nil
}

// executeBatch executes a batch of insert statements
func (s *ImportService) executeBatch(batch [][]interface{}) (int, error) {
	if len(batch) == 0 {
		return 0, nil
	}

	// Build the VALUES clause for batch insert
	valueStrings := make([]string, 0, len(batch))
	valueArgs := make([]interface{}, 0, len(batch)*6)

	for i, row := range batch {
		valueStrings = append(valueStrings, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d)",
			i*6+1, i*6+2, i*6+3, i*6+4, i*6+5, i*6+6))
		valueArgs = append(valueArgs, row...)
	}

	// Execute batch insert with ON CONFLICT DO NOTHING for duplicate handling
	query := fmt.Sprintf(`
		INSERT INTO ticks (utc, symbol, ask, bid, askvol, bidvol)
		VALUES %s
		ON CONFLICT (symbol, utc) DO NOTHING`,
		strings.Join(valueStrings, ","))

	result, err := s.db.Exec(context.Background(), query, valueArgs...)
	if err != nil {
		return 0, fmt.Errorf("batch insert failed: %w", err)
	}

	return int(result.RowsAffected()), nil
}

// refreshContinuousAggregates refreshes all continuous aggregates after data import
func (s *ImportService) refreshContinuousAggregates() error {
	aggregates := []string{
		"ohlc_10s", "ohlc_1m", "ohlc_5m", "ohlc_10m", "ohlc_15m",
		"ohlc_30m", "ohlc_1h", "ohlc_4h", "ohlc_1d",
	}

	for _, aggregate := range aggregates {
		_, err := s.db.Exec(context.Background(),
			fmt.Sprintf("CALL refresh_continuous_aggregate('%s', NULL, NULL)", aggregate))
		if err != nil {
			return fmt.Errorf("failed to refresh %s: %w", aggregate, err)
		}
	}

	return nil
}
