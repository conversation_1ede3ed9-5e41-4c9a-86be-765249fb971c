package services

import (
	"context"
	"fmt"

	"continuum/backend/internal/models"

	"github.com/jackc/pgx/v5/pgxpool"
)

// StatsService handles database statistics operations
type StatsService struct {
	db *pgxpool.Pool
}

// NewStatsService creates a new statistics service
func NewStatsService(db *pgxpool.Pool) *StatsService {
	return &StatsService{db: db}
}

// GetDatabaseStats returns comprehensive database statistics
func (s *StatsService) GetDatabaseStats() (*models.DatabaseStats, error) {
	ctx := context.Background()

	stats := &models.DatabaseStats{}

	// Get total tick count
	err := s.db.QueryRow(ctx, "SELECT COUNT(*) FROM ticks").Scan(&stats.TotalTicks)
	if err != nil {
		return nil, fmt.Errorf("failed to get total ticks: %w", err)
	}

	// Get total instruments
	err = s.db.QueryRow(ctx, "SELECT COUNT(DISTINCT symbol) FROM ticks").Scan(&stats.TotalInstruments)
	if err != nil {
		return nil, fmt.Errorf("failed to get total instruments: %w", err)
	}

	// Get date range
	err = s.db.QueryRow(ctx, "SELECT MIN(utc), MAX(utc) FROM ticks").Scan(&stats.OldestRecord, &stats.NewestRecord)
	if err != nil {
		return nil, fmt.Errorf("failed to get date range: %w", err)
	}

	// Get database size
	var dbSize int64
	err = s.db.QueryRow(ctx, `
		SELECT pg_database_size(current_database())
	`).Scan(&dbSize)
	if err != nil {
		return nil, fmt.Errorf("failed to get database size: %w", err)
	}
	stats.DatabaseSize = formatBytes(dbSize)

	// Get instrument statistics
	instruments, err := s.GetInstrumentStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get instrument stats: %w", err)
	}
	stats.Instruments = instruments

	// Get performance metrics
	performance, err := s.getPerformanceMetrics()
	if err != nil {
		return nil, fmt.Errorf("failed to get performance metrics: %w", err)
	}
	stats.Performance = *performance

	// Get continuous aggregate info
	continuousAggs, err := s.getContinuousAggregateInfo()
	if err != nil {
		return nil, fmt.Errorf("failed to get continuous aggregate info: %w", err)
	}
	stats.ContinuousAggs = continuousAggs

	return stats, nil
}

// GetInstrumentStats returns statistics for all instruments
func (s *StatsService) GetInstrumentStats() ([]models.InstrumentStats, error) {
	ctx := context.Background()

	query := `
		SELECT 
			symbol,
			COUNT(*) as tick_count,
			MIN(utc) as start_date,
			MAX(utc) as end_date,
			MIN(ask) as min_ask,
			MAX(ask) as max_ask,
			MIN(bid) as min_bid,
			MAX(bid) as max_bid,
			AVG(ask - bid) as avg_spread,
			MAX(utc) as last_updated
		FROM ticks 
		GROUP BY symbol 
		ORDER BY symbol
	`

	rows, err := s.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query instrument stats: %w", err)
	}
	defer rows.Close()

	var instruments []models.InstrumentStats

	for rows.Next() {
		var inst models.InstrumentStats
		err := rows.Scan(
			&inst.SymbolID,
			&inst.TickCount,
			&inst.StartDate,
			&inst.EndDate,
			&inst.MinAsk,
			&inst.MaxAsk,
			&inst.MinBid,
			&inst.MaxBid,
			&inst.AvgSpread,
			&inst.LastUpdated,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan instrument stats: %w", err)
		}

		// Map symbol ID to symbol name
		if symbolName, exists := models.ReverseSymbolMapping[inst.SymbolID]; exists {
			inst.Symbol = symbolName
		} else {
			inst.Symbol = fmt.Sprintf("UNKNOWN_%d", inst.SymbolID)
		}

		instruments = append(instruments, inst)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating instrument stats: %w", err)
	}

	return instruments, nil
}

// getPerformanceMetrics returns database performance metrics
func (s *StatsService) getPerformanceMetrics() (*models.DatabasePerformance, error) {
	ctx := context.Background()

	performance := &models.DatabasePerformance{}

	// Get chunk count
	err := s.db.QueryRow(ctx, `
		SELECT COUNT(*) 
		FROM timescaledb_information.chunks 
		WHERE hypertable_name = 'ticks'
	`).Scan(&performance.ChunkCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get chunk count: %w", err)
	}

	// Get table and index sizes
	var tableSize, indexSize int64
	err = s.db.QueryRow(ctx, `
		SELECT 
			pg_total_relation_size('ticks') as table_size,
			pg_indexes_size('ticks') as index_size
	`).Scan(&tableSize, &indexSize)
	if err != nil {
		return nil, fmt.Errorf("failed to get table sizes: %w", err)
	}

	performance.TableSize = formatBytes(tableSize)
	performance.IndexSize = formatBytes(indexSize)

	// Calculate compression ratio (approximate)
	if tableSize > 0 {
		// Rough estimate: uncompressed size would be ~8x larger for TimescaleDB
		performance.CompressionRatio = 8.0
	}

	performance.AvgQueryTime = "< 1s" // Placeholder - would need query performance monitoring

	return performance, nil
}

// getContinuousAggregateInfo returns information about continuous aggregates
func (s *StatsService) getContinuousAggregateInfo() ([]models.ContinuousAggregateInfo, error) {
	ctx := context.Background()

	timeframes := []string{"10s", "1m", "5m", "10m", "15m", "30m", "1h", "4h", "1d"}
	var aggregates []models.ContinuousAggregateInfo

	for _, timeframe := range timeframes {
		viewName := fmt.Sprintf("ohlc_%s", timeframe)

		var bucketCount int64
		err := s.db.QueryRow(ctx, fmt.Sprintf("SELECT COUNT(*) FROM %s", viewName)).Scan(&bucketCount)
		if err != nil {
			// If view doesn't exist, skip it
			continue
		}

		aggregate := models.ContinuousAggregateInfo{
			Name:        viewName,
			Timeframe:   timeframe,
			BucketCount: bucketCount,
			RefreshLag:  "Real-time", // TimescaleDB continuous aggregates are typically real-time
		}

		// Calculate compression ratio vs raw ticks
		if bucketCount > 0 {
			var totalTicks int64
			s.db.QueryRow(ctx, "SELECT COUNT(*) FROM ticks").Scan(&totalTicks)
			if totalTicks > 0 {
				aggregate.CompressionRatio = float64(totalTicks) / float64(bucketCount)
			}
		}

		aggregates = append(aggregates, aggregate)
	}

	return aggregates, nil
}

// formatBytes converts bytes to human readable format
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
