package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"

	"continuum/backend/internal/services"
)

// StatsCacheHandler handles cached statistics operations
type StatsCacheHandler struct {
	statsCacheService *services.StatsCacheService
}

// NewStatsCacheHandler creates a new stats cache handler
func NewStatsCacheHandler(statsCacheService *services.StatsCacheService) *StatsCacheHandler {
	return &StatsCacheHandler{
		statsCacheService: statsCacheService,
	}
}

// GetCachedDatabaseStats handles requests for cached database statistics
func (h *StatsCacheHandler) GetCachedDatabaseStats(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	stats, err := h.statsCacheService.GetCachedDatabaseStats()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get cached database stats: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// UpdateStatsCache handles requests to refresh the statistics cache
func (h *StatsCacheHandler) UpdateStatsCache(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get triggered_by from request body or default to "manual"
	var request struct {
		TriggeredBy string `json:"triggered_by"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		// If no body or invalid JSON, default to manual
		request.TriggeredBy = "manual"
	}
	
	if request.TriggeredBy == "" {
		request.TriggeredBy = "manual"
	}

	err := h.statsCacheService.UpdateStatsCache(request.TriggeredBy)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to update stats cache: %v", err), http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"success": true,
		"message": "Statistics cache updated successfully",
		"triggered_by": request.TriggeredBy,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// GetStatsUpdateLog handles requests for stats update history
func (h *StatsCacheHandler) GetStatsUpdateLog(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// This would query the stats_update_log table
	// For now, return a placeholder response
	response := map[string]interface{}{
		"message": "Stats update log endpoint not yet implemented",
		"note": "This will show the history of statistics cache updates",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
