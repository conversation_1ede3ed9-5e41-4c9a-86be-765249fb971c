package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"

	"continuum/backend/internal/models"
	"continuum/backend/internal/services"
)

// ImportHandler handles file import operations
type ImportHandler struct {
	importService *services.ImportService
}

// NewImportHandler creates a new import handler
func NewImportHandler(importService *services.ImportService) *ImportHandler {
	return &ImportHandler{
		importService: importService,
	}
}

// UploadFile handles file upload to temporary directory
func (h *ImportHandler) UploadFile(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse multipart form (32MB max)
	err := r.ParseMultipartForm(32 << 20)
	if err != nil {
		http.Error(w, "Failed to parse form", http.StatusBadRequest)
		return
	}

	// Get the file from form
	file, header, err := r.FormFile("file")
	if err != nil {
		http.Error(w, "Failed to get file from form", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Validate file extension
	if filepath.Ext(header.Filename) != ".csv" {
		http.Error(w, "Only CSV files are allowed", http.StatusBadRequest)
		return
	}

	// Extract symbol from filename
	symbol, symbolID, ok := models.GetSymbolFromFilename(header.Filename)
	if !ok {
		http.Error(w, "Unable to extract currency pair from filename", http.StatusBadRequest)
		return
	}

	// Create temporary file
	tmpFile, err := os.CreateTemp("/tmp", "continuum_*.csv")
	if err != nil {
		http.Error(w, "Failed to create temporary file", http.StatusInternalServerError)
		return
	}
	defer tmpFile.Close()

	// Copy uploaded file to temporary file
	_, err = io.Copy(tmpFile, file)
	if err != nil {
		os.Remove(tmpFile.Name())
		http.Error(w, "Failed to save file", http.StatusInternalServerError)
		return
	}

	// Return upload success with file info
	response := map[string]interface{}{
		"success":   true,
		"message":   "File uploaded successfully",
		"filename":  header.Filename,
		"temp_path": tmpFile.Name(),
		"symbol":    symbol,
		"symbol_id": symbolID,
		"size":      header.Size,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// PreviewFile handles file preview requests
func (h *ImportHandler) PreviewFile(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		TempPath string `json:"temp_path"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if request.TempPath == "" {
		http.Error(w, "temp_path is required", http.StatusBadRequest)
		return
	}

	// Validate that the file exists and is in /tmp
	if !filepath.HasPrefix(request.TempPath, "/tmp/") {
		http.Error(w, "Invalid file path", http.StatusBadRequest)
		return
	}

	// Get file preview
	preview, err := h.importService.PreviewFile(request.TempPath)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to preview file: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(preview)
}

// ExecuteImport handles import execution requests
func (h *ImportHandler) ExecuteImport(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		TempPath string `json:"temp_path"`
		SymbolID int    `json:"symbol_id"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if request.TempPath == "" {
		http.Error(w, "temp_path is required", http.StatusBadRequest)
		return
	}

	if request.SymbolID == 0 {
		http.Error(w, "symbol_id is required", http.StatusBadRequest)
		return
	}

	// Validate that the file exists and is in /tmp
	if !filepath.HasPrefix(request.TempPath, "/tmp/") {
		http.Error(w, "Invalid file path", http.StatusBadRequest)
		return
	}

	// Execute the import
	result, err := h.importService.ExecuteImport(request.TempPath, request.SymbolID)
	if err != nil {
		// Clean up temp file on error
		os.Remove(request.TempPath)
		http.Error(w, fmt.Sprintf("Import failed: %v", err), http.StatusInternalServerError)
		return
	}

	// Clean up temp file on success
	os.Remove(request.TempPath)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// GetImportHistory handles requests for import history (placeholder for future implementation)
func (h *ImportHandler) GetImportHistory(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse query parameters
	symbolIDStr := r.URL.Query().Get("symbol_id")
	limit := r.URL.Query().Get("limit")

	response := map[string]interface{}{
		"message": "Import history endpoint not yet implemented",
		"params": map[string]string{
			"symbol_id": symbolIDStr,
			"limit":     limit,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
