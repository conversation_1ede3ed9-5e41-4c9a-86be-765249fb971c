package main

import (
	"fmt"
	"log"
	"net/http"

	"continuum/backend/internal/config"
	"continuum/backend/internal/database"
	"continuum/backend/internal/handlers"
	"continuum/backend/internal/services"

	"github.com/rs/cors"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database connections
	db, err := database.NewConnections(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database connections: %v", err)
	}
	defer db.Close()

	// Initialize services
	importService := services.NewImportService(db.MktData)
	statsService := services.NewStatsService(db.MktData)
	statsCacheService := services.NewStatsCacheService(db.MktData, db.Results)

	// Initialize handlers
	importHandler := handlers.NewImportHandler(importService)
	statsHandler := handlers.NewStatsHandler(statsService)
	statsCacheHandler := handlers.NewStatsCacheHandler(statsCacheService)

	// Setup routes
	mux := http.NewServeMux()

	// Data management endpoints
	mux.HandleFunc("/api/import/upload", importHandler.UploadFile)
	mux.HandleFunc("/api/import/preview", importHandler.PreviewFile)
	mux.HandleFunc("/api/import/execute", importHandler.ExecuteImport)

	// Statistics endpoints (cached)
	mux.HandleFunc("/api/stats/database", statsCacheHandler.GetCachedDatabaseStats)
	mux.HandleFunc("/api/stats/instruments", statsHandler.GetInstrumentStats)
	mux.HandleFunc("/api/stats/update", statsCacheHandler.UpdateStatsCache)
	mux.HandleFunc("/api/stats/log", statsCacheHandler.GetStatsUpdateLog)

	// Legacy stats endpoints (direct queries - for debugging)
	mux.HandleFunc("/api/stats/database/direct", statsHandler.GetDatabaseStats)

	// Health check
	mux.HandleFunc("/api/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, `{"status":"healthy","service":"continuum-api"}`)
	})

	// Setup CORS
	handler := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000", "http://localhost:3001", "http://***********:3000", "http://***********:3000"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	}).Handler(mux)

	fmt.Printf("🚀 Continuum API server starting on port %s\n", cfg.Port)
	fmt.Printf("📊 Connected to TimescaleDB: %s\n", cfg.MktDataDB)
	fmt.Printf("📈 Connected to Results DB: %s\n", cfg.ResultsDB)

	if err := http.ListenAndServe(":"+cfg.Port, handler); err != nil {
		log.Fatalf("Could not start server: %s\n", err)
	}
}
