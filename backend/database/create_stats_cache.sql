-- Create stats cache tables in the results database
-- Connect to the results database first

-- Database statistics cache
CREATE TABLE IF NOT EXISTS database_stats (
    id SERIAL PRIMARY KEY,
    total_ticks BIGINT NOT NULL,
    total_instruments INTEGER NOT NULL,
    database_size TEXT NOT NULL,
    oldest_record TIMESTAMP WITH TIME ZONE NOT NULL,
    newest_record TIMESTAMP WITH TIME ZONE NOT NULL,
    chunk_count INTEGER NOT NULL,
    table_size TEXT NOT NULL,
    index_size TEXT NOT NULL,
    compression_ratio REAL NOT NULL DEFAULT 8.0,
    avg_query_time TEXT NOT NULL DEFAULT '< 1s',
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Instrument statistics cache
CREATE TABLE IF NOT EXISTS instrument_stats (
    id SERIAL PRIMARY KEY,
    symbol TEXT NOT NULL,
    symbol_id INTEGER NOT NULL,
    tick_count BIGINT NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    min_ask DOUBLE PRECISION NOT NULL,
    max_ask DOUBLE PRECISION NOT NULL,
    min_bid DOUBLE PRECISION NOT NULL,
    max_bid DOUBLE PRECISION NOT NULL,
    avg_spread DOUBLE PRECISION NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(symbol_id)
);

-- Continuous aggregate statistics cache
CREATE TABLE IF NOT EXISTS continuous_aggregate_stats (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    timeframe TEXT NOT NULL,
    bucket_count BIGINT NOT NULL,
    refresh_lag TEXT NOT NULL DEFAULT 'Real-time',
    compression_ratio DOUBLE PRECISION NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(name)
);

-- Stats update log
CREATE TABLE IF NOT EXISTS stats_update_log (
    id SERIAL PRIMARY KEY,
    update_type TEXT NOT NULL, -- 'manual', 'auto', 'import'
    duration_seconds REAL NOT NULL,
    records_updated INTEGER NOT NULL,
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    triggered_by TEXT, -- user, system, import
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_database_stats_last_updated ON database_stats(last_updated DESC);
CREATE INDEX IF NOT EXISTS idx_instrument_stats_symbol_id ON instrument_stats(symbol_id);
CREATE INDEX IF NOT EXISTS idx_instrument_stats_last_updated ON instrument_stats(last_updated DESC);
CREATE INDEX IF NOT EXISTS idx_continuous_aggregate_stats_name ON continuous_aggregate_stats(name);
CREATE INDEX IF NOT EXISTS idx_stats_update_log_created_at ON stats_update_log(created_at DESC);

-- Insert initial placeholder data (will be updated by the stats service)
INSERT INTO database_stats (
    total_ticks, total_instruments, database_size, 
    oldest_record, newest_record, chunk_count,
    table_size, index_size
) VALUES (
    0, 0, '0 B',
    '1970-01-01 00:00:00+00', '1970-01-01 00:00:00+00', 0,
    '0 B', '0 B'
) ON CONFLICT DO NOTHING;

-- Function to get the latest database stats
CREATE OR REPLACE FUNCTION get_latest_database_stats()
RETURNS TABLE (
    total_ticks BIGINT,
    total_instruments INTEGER,
    database_size TEXT,
    oldest_record TIMESTAMP WITH TIME ZONE,
    newest_record TIMESTAMP WITH TIME ZONE,
    chunk_count INTEGER,
    table_size TEXT,
    index_size TEXT,
    compression_ratio REAL,
    avg_query_time TEXT,
    last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.total_ticks,
        ds.total_instruments,
        ds.database_size,
        ds.oldest_record,
        ds.newest_record,
        ds.chunk_count,
        ds.table_size,
        ds.index_size,
        ds.compression_ratio,
        ds.avg_query_time,
        ds.last_updated
    FROM database_stats ds
    ORDER BY ds.last_updated DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;
