# Continuum Trading Platform - Project Documentation

## Project Overview

**Continuum** is a comprehensive trading platform for forex tick data management, strategy development, backtesting, and live trading execution.

### Core Objectives
- Import and manage high-frequency tick data in TimescaleDB
- Visual strategy builder for trading algorithm development
- Historical backtesting with detailed performance analytics
- Production-grade strategy testing with RabbitMQ messaging
- Live demo account trading integration

### Technology Stack
- **Backend APIs**: Golang v1.24.x
- **Frontend**: Next.js v15.3.x
- **Databases**: PostgreSQL 17.5 + TimescaleDB 2.21.0
- **Messaging**: RabbitMQ (AMQP)
- **Development Environment**: Remote VS Code → Plutus Server

### Infrastructure Details
- **Development Server**: Plutus (POP_OS 22.04 / Ubuntu 22.04)
- **Server IP**: ***********
- **Server Credentials**: mark / mark
- **Client**: Windows 11 Pro with VS Code Remote SSH
- **All services run locally on Plutus server**

## Development Phases

### Phase 1: Data Management System
**Objective**: CSV tick data import and TimescaleDB management

#### Features
- **File Upload Interface**: Web-based CSV file upload to `/tmp` directory
- **Data Validation**: Parse and validate CSV structure before import
- **Import Preview**: Display date range and record count for user confirmation
- **Duplicate Prevention**: Ensure only unique records using `UNIQUE (symbol, utc)` constraint
- **Continuous Aggregate Refresh**: Auto-update all OHLC timeframes after import
- **Database Statistics**: Real-time dashboard showing:
  - Total tick records per instrument
  - Date ranges for each currency pair
  - Database size and performance metrics

#### CSV File Format
```
Time (UTC),Ask,Bid,AskVol,BidVol
2025-01-15 09:30:00.123,1.08456,1.08454,1000000,1500000
```

#### Symbol Mapping (from filename)
- **EURUSD_Ticks_*.csv** → Symbol ID: 1
- **USDJPY_Ticks_*.csv** → Symbol ID: 2
- **GBPUSD_Ticks_*.csv** → Symbol ID: 3
- **USDCHF_Ticks_*.csv** → Symbol ID: 4

#### Import Workflow
1. Upload CSV → `/tmp` directory
2. Golang API parses file and extracts metadata
3. Display preview with date range and record count
4. User confirms import
5. Data inserted with duplicate checking
6. Continuous aggregates refreshed
7. Temporary file deleted
8. Statistics updated

### Phase 2: Strategy Backtesting Engine
**Objective**: Visual strategy builder with historical data testing

#### Features
- **Visual Strategy Builder**: Drag-and-drop interface for strategy creation
  - Data source selection (tick, OHLC timeframes)
  - Technical indicator configuration
  - Conditional rule builder (entry/exit logic)
  - Risk management settings (stop loss, take profit, trailing stops)
- **Strategy Testing**: Execute strategies against historical data
  - TimescaleDB query optimization for large datasets
  - Chronological data processing (UTC timestamp order)
  - Real-time simulation of market conditions
- **Results Analysis**: Comprehensive performance metrics
  - Individual trade details (entry/exit, duration, P&L)
  - Strategy performance statistics
  - Pip-based profit/loss calculations
  - Maximum drawdown, win rate, risk/reward ratios

#### Strategy Definition Components
- **Entry Conditions**: Price action, technical indicators, volume
- **Exit Conditions**: Take profit, stop loss, trailing stops, time-based
- **Position Sizing**: Fixed lots, percentage-based, volatility-adjusted
- **Risk Management**: Maximum daily loss, position limits

#### Data Processing Flow
1. Strategy definition submitted to Golang API
2. API generates optimized TimescaleDB queries
3. Historical data retrieved in chronological order
4. Strategy logic executed against each data point
5. Trades recorded in `results` database
6. Performance metrics calculated and returned
7. Results displayed in frontend analytics dashboard

#### Results Storage
- **Strategy Definitions**: Immutable strategy configurations
- **Backtest Runs**: Unique test instances with parameters
- **Trade Records**: Individual trade details and performance
- **Performance Metrics**: Aggregated statistics per backtest

### Phase 3: Production Strategy Testing Platform
**Objective**: RabbitMQ-based strategy testing with simulated trading environment

#### Features
- **Message-Driven Architecture**: RabbitMQ AMQP queues for strategy execution
- **Simulated Trading Backend**: Dummy Oanda/JForex Golang service
- **Account Management**: Virtual trading account with balance tracking
- **Real-time Position Monitoring**: Live position updates and P&L tracking
- **Strategy Execution Engine**: Separate Golang API consuming market data
- **Performance Analytics**: Detailed account and position metrics

#### Architecture Components
- **Data Publisher**: Publishes historical data to RabbitMQ exchange
- **Strategy Consumer**: Consumes data and executes trading logic
- **Trading Backend**: Simulated broker handling orders and positions
- **Account Service**: Manages virtual account balance and positions
- **Results Recorder**: Stores detailed trading metrics in `results` database

#### Message Flow
1. Historical data published to RabbitMQ exchange
2. Strategy service consumes data messages
3. Trading signals sent to simulated broker via AMQP
4. Broker executes orders and updates positions
5. Account updates published back to strategy service
6. All metrics recorded in `results` database

### Phase 4: Live Demo Trading
**Objective**: Real demo account integration with existing AMQP infrastructure

#### Features
- **Live Demo Integration**: Connect to Oanda/JForex demo accounts
- **Strategy Selection**: Execute any previously tested strategy
- **Real Market Data**: Live price feeds instead of historical data
- **Risk Management**: Real-time position and account monitoring
- **Performance Tracking**: Live trading results and analytics

#### Integration Points
- **Existing AMQP Code**: Leverage current trading instruction infrastructure
- **Demo Account APIs**: Oanda REST API / JForex platform integration
- **Message Adaptation**: Modify AMQP messages for live demo accounts
- **Real-time Monitoring**: Live position and account status updates

## Technical Architecture

### Database Design

#### TimescaleDB (`mktdata`) - Market Data
- **Primary Purpose**: High-frequency tick data storage and OHLC aggregation
- **Hypertable Configuration**: 7-day chunks, partitioned by symbol
- **Continuous Aggregates**: 9 timeframes (10s, 1m, 5m, 10m, 15m, 30m, 1h, 4h, 1d)
- **Performance**: 150M+ ticks, sub-second query times

#### PostgreSQL (`results`) - Trading Results
- **Primary Purpose**: Strategy definitions, backtest results, trade records
- **Schema Design**: Normalized relational structure
- **Key Tables**: strategies, backtests, trades, performance_metrics

### API Architecture

#### Golang Services
- **Data Management API**: CSV import, database statistics
- **Backtesting API**: Strategy execution, historical data processing
- **Strategy Engine API**: RabbitMQ consumer for production testing
- **Trading Backend API**: Simulated broker for order execution

#### Next.js Frontend
- **Data Management Dashboard**: Upload interface, database statistics
- **Strategy Builder**: Visual strategy creation interface
- **Results Analytics**: Performance charts and trade analysis
- **Live Trading Monitor**: Real-time position and account tracking

### Message Queue Architecture

#### RabbitMQ Configuration (**********)
- **Market Data Exchange**: Historical tick data distribution
- **Trading Signals Queue**: Strategy-generated buy/sell signals
- **Account Updates Queue**: Position and balance notifications
- **Results Queue**: Trade execution confirmations

# TimescaleDB Setup - Reference Information

## System Information
- **Linux Host**: Plutus
- **Server IP**: ***********
- **PostgreSQL Version**: 17.5 (Ubuntu 17.5-1.pgdg22.04+1)
- **TimescaleDB Version**: 2.21.0
- **Timescale Database**: `mktdata`
- **Postgres User**: `postgres`
- **Postgres Password**: 'postgres'
- **OS**: POP_OS! based on Ubuntu 22.04 LTS, x86_64
- **RabitMQ Server Details**: host: ********** user: mark password: mark 

## Table Structure

### Main Ticks Table: `ticks`
```sql
Column |           Type           | Nullable | Notes
-------|--------------------------|----------|-------
utc    | timestamp with time zone | NOT NULL | Primary time column
symbol | integer                  | NOT NULL | Currency pair ID (EURUSD=1, USDJPY=2, GBPUSD=3, USDCHF=4)
ask    | double precision         | NOT NULL | Ask price
bid    | double precision         | NOT NULL | Bid price
askvol | real                     | NULL     | Ask volume
bidvol | real                     | NULL     | Bid volume
```

**Constraints:**
- `UNIQUE (symbol, utc)` - Prevents duplicate ticks
- **Hypertable**: 7-day chunks, partitioned by symbol
- **Child tables**: 290 chunks

### Staging Table: `ticks_staging`
Same structure as `ticks` but all columns nullable for import processing.

## Continuous Aggregates (OHLC Views)

All 9 timeframes have identical structure:
- `ohlc_10s`, `ohlc_1m`, `ohlc_5m`, `ohlc_10m`, `ohlc_15m`, `ohlc_30m`, `ohlc_1h`, `ohlc_4h`, `ohlc_1d`

```sql
Column     | Type                     | Description
-----------|--------------------------|-------------
symbol     | integer                  | Currency pair ID
bucket     | timestamp with time zone | Time bucket start
open_ask   | double precision         | First ask in bucket
high_ask   | double precision         | Highest ask in bucket
low_ask    | double precision         | Lowest ask in bucket
close_ask  | double precision         | Last ask in bucket
open_bid   | double precision         | First bid in bucket
high_bid   | double precision         | Highest bid in bucket
low_bid    | double precision         | Lowest bid in bucket
close_bid  | double precision         | Last bid in bucket
askvol     | real                     | Sum of ask volumes
bidvol     | real                     | Sum of bid volumes
tick_count | bigint                   | Number of ticks in bucket
```

## Data Summary (EURUSD - Symbol 1)
- **Total Rows**: 150,590,110 ticks
- **Date Range**: 2019-12-31 to 2025-07-15
- **Price Range**: Ask 0.95363-1.23495, Bid 0.95357-1.23494

## Performance Benchmarks
| Timeframe | Bucket Count | Query Time | Compression |
|-----------|--------------|------------|-------------|
| Raw Ticks | 150,590,110  | 2.063s     | 1:1         |
| 10s       | 11,251,095   | 333ms      | 13:1        |
| 1m        | 2,061,972    | 86ms       | 73:1        |
| 5m        | 414,651      | 35ms       | 363:1       |
| 1h        | 34,565       | 15ms       | 4,356:1     |
| 1d        | 1,733        | 12ms       | 86,888:1    |

## Critical Query Syntax Notes

### ❌ WRONG - psql commands in SQL
```sql
-- This FAILS in psql -c "..."
\echo 'message'
\d table_name
```

### ✅ CORRECT - Use separate psql script files
```sql
-- In .sql files, these work fine
\echo 'message'
\d table_name
```

### ❌ WRONG - Continuous aggregates in transactions
```sql
BEGIN;
CREATE MATERIALIZED VIEW ... WITH (timescaledb.continuous) AS ...;
COMMIT; -- FAILS!
```

### ✅ CORRECT - Continuous aggregates outside transactions
```sql
-- No BEGIN/COMMIT around continuous aggregate creation
CREATE MATERIALIZED VIEW ... WITH (timescaledb.continuous) AS ...;
```

## Essential Functions

### Import Function
```sql
SELECT import_eurusd_file('/path/to/file.csv') -- Returns rows inserted
```

### Sample Queries
```sql
-- Complete OHLC with volumes
SELECT bucket, open_ask, high_ask, low_ask, close_ask, 
       open_bid, high_bid, low_bid, close_bid,
       askvol, bidvol, tick_count
FROM ohlc_1m WHERE symbol = 1 ORDER BY bucket DESC LIMIT 5;

-- Performance test with timing
\timing on
SELECT COUNT(*) FROM ticks WHERE symbol = 1;
\timing off
```

## File Locations
- **Main setup**: `database/eurusd_complete_setup.sql`
- **Continuous aggregates**: `database/create_continuous_aggregates.sql`
- **Verification**: `database/verify_setup.sql`

## Symbol Mapping
- **EURUSD**: 1
- **USDJPY**: 2 (planned)
- **GBPUSD**: 3 (planned)
- **USDCHF**: 4 (planned)

## Development Roadmap

### Immediate Next Steps
1. **Project Setup**: Initialize Golang and Next.js project structure
2. **Database Schema**: Design `results` database tables
3. **Phase 1 Implementation**: Start with data management system
4. **API Design**: Define REST endpoints and data structures

### ✅ Technical Decisions - CONFIRMED

#### Frontend Stack
- **UI Framework**: Tailwind CSS for styling
- **Component Library**: Headless UI or Radix UI for advanced components
- **Charts**: TradingView Lightweight Charts for financial data visualization
- **Forms**: React Hook Form for complex strategy builder forms

#### Technical Indicators Architecture
- **Modular Design**: Separate modules for each indicator/metric
- **Database-Driven Calculations**: TimescaleDB performs heavy lifting
- **JSON Contracts**: Strategy definitions as portable JSON configurations
- **Dynamic Hydration**: Runtime configuration of indicator parameters

#### Core Technical Indicators
- **Moving Averages**: SMA, EMA
- **Momentum**: RSI, MACD
- **Volatility**: Bollinger Bands, ATR, Donchian Channels
- **Volume**: OBV, TVWAP (both volume-based and tick-count versions)
- **Price-Derived Metrics**: Distance calculations (price to bands, channels, MAs)

#### Strategy Builder
- **Advanced Visual Programming**: Drag-and-drop with dropdown selectors
- **Component Types**: Date ranges, price sources, TI/metrics, OHLC periods
- **Conditional Logic Builder**: Visual rule creation interface
- **JSON Contract Output**: Complete strategy definition for API consumption

#### Infrastructure Status
- **Backend**: ✅ Golang project structure complete with handlers, services, models
- **Frontend**: ✅ Next.js v15.3.x with Tailwind CSS installed
- **Build Status**: ✅ Backend builds successfully without errors
- **RabbitMQ**: ✅ Ready and operational at **********
- **OANDA/JForex**: ✅ Existing AMQP integration (Phase 4)
- **Development Order**: Phase 1 → Phase 2 → Phase 3 → Phase 4

## Current Project Structure

### Backend (Golang)
```
backend/
├── main.go                    # Main application entry point
├── go.mod                     # Go module dependencies
├── go.sum                     # Go module checksums
└── internal/
    ├── config/                # Configuration management
    ├── database/              # Database connection and utilities
    ├── handlers/              # HTTP request handlers
    │   └── import.go          # File import API endpoints
    ├── models/                # Data models and structures
    └── services/              # Business logic services
        ├── import.go          # CSV import service
        └── stats.go           # Database statistics service
```

### Frontend (Next.js)
```
frontend/
├── package.json               # Node.js dependencies
├── tailwind.config.js         # Tailwind CSS configuration
├── next.config.js             # Next.js configuration
└── [Next.js project structure with Tailwind CSS]
```

### Development Status
- ✅ **Backend API**: Complete with all endpoints tested and working
- ✅ **Frontend Application**: Full Next.js app with professional UI
- ✅ **Phase 1 Complete**: Data Management System fully implemented
- ✅ **Integration Tested**: Complete upload → preview → import → statistics workflow
- 🚀 **Ready for Phase 2**: Strategy Backtesting Engine development

### Phase 1 Implementation - COMPLETE ✅
**Data Management System Features:**
- ✅ **File Upload Interface**: Drag-and-drop CSV upload with validation
- ✅ **Import Preview**: File metadata, date range, record count, sample data display
- ✅ **Import Execution**: Progress tracking, duplicate prevention, result display
- ✅ **Database Statistics**: Real-time dashboard with comprehensive metrics
- ✅ **API Integration**: Full frontend-backend communication
- ✅ **Error Handling**: Comprehensive error handling and user feedback
- ✅ **Professional UI**: Modern, responsive design with Tailwind CSS

**Tested Workflow:**
1. Upload CSV file → Validation and temporary storage
2. Preview file → Display metadata and sample data
3. Confirm import → Execute with progress tracking
4. View results → Import statistics and performance metrics
5. Database statistics → Real-time dashboard with all metrics

**Recent Fixes:**
- ✅ Fixed 404 error on database statistics page (CORS and network interface issue)
- ✅ Added copyright footer to all pages ("Copyright 2025 Mark Smith")
- ✅ Implemented stats caching system with 'Update Stats' button
- ✅ Optimized database performance queries for large datasets
- ✅ Fixed symbol mapping issue (database stores integers, not strings)

**Stats Caching System:**
- ✅ **Cache Tables**: Created in `results` database for fast statistics loading
- ✅ **Update Stats Button**: Manual refresh of cached statistics from TimescaleDB
- ✅ **Performance**: Statistics page loads instantly from cache
- ✅ **Auto-Update**: Can be triggered after imports to keep stats current
- ✅ **Logging**: Tracks all cache updates with timing and success/failure

## Lessons Learned
1. **Never run 4-hour scripts** - Always break into testable chunks
2. **Test with small datasets first** - Use single currency pair
3. **Continuous aggregates must be outside transactions**
4. **Use .sql files for complex queries** - Not psql -c "..."
5. **Always include volume columns in OHLC queries**
6. **Verify data structure before assuming it's wrong**
